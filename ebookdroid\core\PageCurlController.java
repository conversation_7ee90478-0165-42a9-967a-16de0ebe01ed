package org.ebookdroid.core;

import android.animation.ValueAnimator;
import android.graphics.Rect;
import android.graphics.RectF;
import android.view.MotionEvent;
import android.view.animation.DecelerateInterpolator;

import com.tct.exbook.android.utils.LOG;
import com.tct.exbook.model.AppBook;

import org.ebookdroid.common.settings.SettingsManager;
import org.ebookdroid.common.settings.types.DocumentViewMode;
import org.ebookdroid.common.settings.types.PageAlign;
import org.ebookdroid.ui.viewer.IActivityController;

public class PageCurlController extends HScrollController {

    // 翻页动画状态
    public enum CurlState {
        NONE,           // 无动画
        CURLING,        // 正在翻页
        ANIMATING       // 自动完成动画
    }

    // 翻页方向
    public enum CurlDirection {
        LEFT_TO_RIGHT,  // 从左到右（下一页）
        RIGHT_TO_LEFT   // 从右到左（上一页）
    }

    // 动画状态变量
    private CurlState curlState = CurlState.NONE;
    private CurlDirection curlDirection = CurlDirection.LEFT_TO_RIGHT;
    private float curlProgress = 0.0f;  // 翻页进度 0.0 - 1.0
    private float touchX = 0.0f;        // 当前触摸X坐标
    private float touchY = 0.0f;        // 当前触摸Y坐标
    private float startTouchX = 0.0f;   // 初始触摸X坐标
    private float startTouchY = 0.0f;   // 初始触摸Y坐标
    private ValueAnimator curlAnimator; // 翻页动画器

    // 翻页阈值
    private static final float CURL_THRESHOLD = 0.3f;  // 超过30%自动完成翻页
    private static final int ANIMATION_DURATION = 300; // 动画持续时间(ms)


    public PageCurlController(final IActivityController base) {
        super(base, DocumentViewMode.PAGE_CURL_SCROLL);
    }

    @Override
    public int getFirstVisiblePage() {
        // 仿真翻页模式下，确保至少显示第一页
        if (model == null) return 0;
        int firstVisible = model.getCurrentDocPageIndex() - 1;
        if (firstVisible < 0) {
            return 0;
        }
        LOG.d("测试仿真翻页","getFirstVisiblePage: " + firstVisible, "model.getPageCount()", model.getPageCount());
        return firstVisible;
//        return firstVisible;
    }

    @Override
    public int getLastVisiblePage() {
        // 仿真翻页模式下，确保至少显示第一页
        if (model == null) return 0;
        int lastVisible = model.getCurrentDocPageIndex() + 1;
        if (lastVisible >= model.getPageCount()) {
            return model.getPageCount() - 1;
        }
        LOG.d("测试仿真翻页","getLastVisiblePage: " + lastVisible, "model.getPageCount()", model.getPageCount());
        return lastVisible;
    }

    @Override
    public final Rect getScrollLimits() {
        // 仿真翻页不需要滚动，固定在原点
        return new Rect(0, 0, 0, 0);
    }

    @Override
    public final ViewState goToPage(final int toPage) {
        // 直接跳转到指定页面，无滚动动画
        final Page page = model.getPageByDocIndex(toPage);
        if (page != null) {
            LOG.d("测试仿真翻页", "跳转到页面|" + toPage);
            model.setCurrentPageIndex(page.index, model.getPageCount());
            // 触发滚动事件来计算页面可见性和解码
            ViewState viewState = new ViewState(this);
            EventPool.newEventScroll(this, 0).process();
            return viewState;
        }
        return null;
    }

    @Override
    public int calculateCurrentPage(final ViewState viewState, final int firstVisible, final int lastVisible) {
        return viewState.model.getCurrentDocPageIndex();
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#invalidatePageSizes(InvalidateSizeReason,
     * Page)
     */
    @Override
    public synchronized final void invalidatePageSizes(final InvalidateSizeReason reason, final Page changedPage) {
        if (!isInitialized) {
            return;
        }

        if (reason == InvalidateSizeReason.PAGE_ALIGN) {
            return;
        }

        final int height = getHeight();
        final int width = getWidth();
        final AppBook bookSettings = SettingsManager.getBookSettings();
        final PageAlign pageAlign = DocumentViewMode.getPageAlign(bookSettings);

        // 所有页面都设置在同一位置 (0,0)
        final RectF pageBounds = calcPageBounds(pageAlign, 1.0f, width, height);

        if (changedPage == null) {
            for (final Page page : model.getPages()) {
                page.setBounds(new RectF(pageBounds));
            }
        } else {
            changedPage.setBounds(new RectF(pageBounds));
        }
    }
    @Override
    public RectF calcPageBounds(final PageAlign pageAlign, final float pageAspectRatio, final int width,
                                final int height) {
        return new RectF(0, 0, width, height);
    }

    /**
     * 重写触摸事件处理，实现仿真翻页
     */
    @Override
    public boolean onTouchEvent(final MotionEvent ev) {
        // 在编辑模式下不处理翻页
        if (com.tct.exbook.model.AppState.get().isEditMode) {
            return super.onTouchEvent(ev);
        }

        // 先处理仿真翻页的触摸事件
        if (handleCurlTouchEvent(ev)) {
            return true;
        }
        // 如果不是翻页手势，交给父类处理
        return super.onTouchEvent(ev);
    }

    /**
     * 处理仿真翻页的触摸事件
     */
    private boolean handleCurlTouchEvent(MotionEvent ev) {
        final float x = ev.getX();
        final float y = ev.getY();
        final int width = getWidth();
        final int height = getHeight();

        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                return handleTouchDown(x, y, width, height);

            case MotionEvent.ACTION_MOVE:
                return handleTouchMove(x, y, width, height);

            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                return handleTouchUp(x, y, width, height);
        }
        return false;
    }

    /**
     * 处理触摸按下事件
     */
    private boolean handleTouchDown(float x, float y, int width, int height) {
        // 检查是否在页面边缘区域（用于翻页）
        final float edgeThreshold = width * 0.1f; // 10% 的边缘区域

        if (x < edgeThreshold) {
            // 左边缘，准备向右翻页（上一页）
            if (model.getCurrentDocPageIndex() > 0) {
                startCurl(x, y, CurlDirection.RIGHT_TO_LEFT);
                return true;
            }
        } else if (x > width - edgeThreshold) {
            // 右边缘，准备向左翻页（下一页）
            if (model.getCurrentDocPageIndex() < model.getPageCount() - 1) {
                startCurl(x, y, CurlDirection.LEFT_TO_RIGHT);
                return true;
            }
        }
        return false;
    }

    /**
     * 处理触摸移动事件
     */
    private boolean handleTouchMove(float x, float y, int width, int height) {
        if (curlState == CurlState.CURLING) {
            updateCurl(x, y, width, height);
            return true;
        }
        return false;
    }

    /**
     * 处理触摸抬起事件
     */
    private boolean handleTouchUp(float x, float y, int width, int height) {
        if (curlState == CurlState.CURLING) {
            finishCurl(width, height);
            return true;
        }
        return false;
    }

    /**
     * 开始翻页
     */
    private void startCurl(float x, float y, CurlDirection direction) {
        LOG.d("测试仿真翻页", "开始翻页|方向=" + direction);
        curlState = CurlState.CURLING;
        curlDirection = direction;
        startTouchX = x;
        startTouchY = y;
        touchX = x;
        touchY = y;
        curlProgress = 0.0f;

        // 停止任何正在进行的动画
        if (curlAnimator != null && curlAnimator.isRunning()) {
            curlAnimator.cancel();
        }

        // 触发重绘
        getView().redrawView();
    }

    /**
     * 更新翻页状态
     */
    private void updateCurl(float x, float y, int width, int height) {
        touchX = x;
        touchY = y;

        // 计算翻页进度
        float deltaX = Math.abs(x - startTouchX);
        curlProgress = Math.min(deltaX / width, 1.0f);

        LOG.d("测试仿真翻页", "更新翻页|进度=" + curlProgress);

        // 触发重绘
        getView().redrawView();
    }

    /**
     * 完成翻页
     */
    private void finishCurl(int width, int height) {
        LOG.d("测试仿真翻页", "完成翻页|进度=" + curlProgress + "|阈值=" + CURL_THRESHOLD);

        if (curlProgress > CURL_THRESHOLD) {
            // 超过阈值，完成翻页
            animateToComplete();
        } else {
            // 未超过阈值，回弹
            animateToCancel();
        }
    }

    /**
     * 动画完成翻页
     */
    private void animateToComplete() {
        curlState = CurlState.ANIMATING;
        curlAnimator = ValueAnimator.ofFloat(curlProgress, 1.0f);
        curlAnimator.setDuration((long) ((1.0f - curlProgress) * ANIMATION_DURATION));
        curlAnimator.setInterpolator(new DecelerateInterpolator());
        curlAnimator.addUpdateListener(animation -> {
            curlProgress = (Float) animation.getAnimatedValue();
            getView().redrawView();
        });
        curlAnimator.addListener(new android.animation.AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(android.animation.Animator animation) {
                // 翻页完成，切换到目标页面
                int currentPage = model.getCurrentDocPageIndex();
                int targetPage = curlDirection == CurlDirection.LEFT_TO_RIGHT ?
                    currentPage + 1 : currentPage - 1;

                LOG.d("测试仿真翻页", "翻页动画完成|目标页面=" + targetPage);
                goToPage(targetPage);
                resetCurlState();
            }
        });
        curlAnimator.start();
    }

    /**
     * 动画取消翻页
     */
    private void animateToCancel() {
        curlState = CurlState.ANIMATING;
        curlAnimator = ValueAnimator.ofFloat(curlProgress, 0.0f);
        curlAnimator.setDuration((long) (curlProgress * ANIMATION_DURATION));
        curlAnimator.setInterpolator(new DecelerateInterpolator());
        curlAnimator.addUpdateListener(animation -> {
            curlProgress = (Float) animation.getAnimatedValue();
            getView().redrawView();
        });
        curlAnimator.addListener(new android.animation.AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(android.animation.Animator animation) {
                resetCurlState();
            }
        });
        curlAnimator.start();
    }

    /**
     * 重置翻页状态
     */
    private void resetCurlState() {
        curlState = CurlState.NONE;
        curlProgress = 0.0f;
        touchX = 0.0f;
        touchY = 0.0f;
        startTouchX = 0.0f;
        startTouchY = 0.0f;
        getView().redrawView();
    }

    // Getter方法供绘制时使用
    public CurlState getCurlState() {
        return curlState;
    }

    public CurlDirection getCurlDirection() {
        return curlDirection;
    }

    public float getCurlProgress() {
        return curlProgress;
    }

    public float getTouchX() {
        return touchX;
    }

    public float getTouchY() {
        return touchY;
    }
}
