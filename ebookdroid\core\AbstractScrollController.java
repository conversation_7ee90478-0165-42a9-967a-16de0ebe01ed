package org.ebookdroid.core;

import android.graphics.RectF;

import com.tct.exbook.android.utils.LOG;
import com.tct.exbook.model.AppAnnotation;
import com.tct.exbook.model.AppBook;
import com.tct.exbook.pdf.info.AnnotationData;
import com.tct.exbook.pdf.info.model.BookCSS;

import org.ebookdroid.common.settings.SettingsManager;
import org.ebookdroid.common.settings.types.DocumentViewMode;
import org.ebookdroid.ui.viewer.IActivityController;

import java.util.List;

public abstract class AbstractScrollController extends AbstractViewController {


    protected AbstractScrollController(final IActivityController base, final DocumentViewMode mode) {
        super(base, mode);
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#goToPage(int)
     */
    @Override
    public ViewState goToPage(final int toPage) {
        return new EventGotoPage(this, toPage, false).process();
    }

    @Override
    public ViewState goToPageAndCenter(int page) {
        return new EventGotoPage(this, page, false, true).process();

    }

    @Override
    public final ViewState goToPage(final int toPage, boolean animate) {
        return new EventGotoPage(this, toPage, animate).process();
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#goToPage(int, float, float)
     */
    @Override
    public final ViewState goToPage(final int toPage, final float offsetX, final float offsetY) {
        return new EventGotoPage(this, toPage, offsetX, offsetY).process();
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#drawView(org.ebookdroid.core.EventDraw)
     */
    @Override
    public final void drawView(final EventDraw eventDraw) {
        final ViewState viewState = eventDraw.viewState;
        LOG.d("测试仿真翻页 ", "AbstractScrollController drawView ");
        if (viewState.model == null) {
            // 测试仿真翻页
            LOG.d("测试仿真翻页 ", "viewstate model == null");
            return;
        }

        LOG.d("测试仿真翻页", "pages", viewState.pages);
        LOG.d("测试仿真翻页", "model.pages.length", viewState.model.getPages().length);
        LOG.d("测试仿真翻页", "currentIndex", viewState.pages.currentIndex);

        if (viewState.ctrl instanceof PageCurlController) {
            // 处理仿真页面
            if (viewState.pages.getCurrentPage() != null) {
                LOG.d("测试仿真翻页", "当前页面", viewState.pages.getCurrentPage());
                Page currentPage = viewState.pages.getCurrentPage();
                loadAnnotations(currentPage, viewState);
                eventDraw.process(currentPage);
            }
        } else {
            for (final Page page : viewState.pages.getVisiblePages()) {

                if (page != null) {
                    LOG.d("测试仿真翻页", page.toString());
                    loadAnnotations(page, viewState);
                    eventDraw.process(page);
                }
            }
        }
        getView().continueScroll();
    }


    /**
     * 加载标注
     *
     * @param page
     * @param viewState
     */
    private void loadAnnotations(Page page, ViewState viewState) {
        if (page.marks.isEmpty()) {
            if (page.texts == null) {
                page.texts = viewState.model.decodeService.getCodecDocument().getPage(page.index.docIndex).getText();
            }
            List<AppAnnotation> annotations = AnnotationData.get().getBookmarksByBook(viewState.book.path);
            // 查找标注位置
            for (AppAnnotation annotation : annotations) {
                LOG.d("测试框架", annotation.page, page.index.docIndex, page.index.viewIndex);
                LOG.d("测试删除 annotation", annotation.text);
                if (BookCSS.get().fontSizeSp == annotation.fontSize) {
                    if (annotation.page == page.index.docIndex) {
                        annotation.annotation = page.getTexts(annotation.startIndex, annotation.endIndex);
                        if (annotation.annotation.isEmpty()) {
                            annotation.annotation = page.getTexts(annotation.textBefore, annotation.textAfter);
                        }
                        if (!annotation.annotation.isEmpty()) {
                            page.marks.add(annotation);
                        }
                    }
                } else {
                    if (annotation.annotation.isEmpty()) {
                        annotation.annotation = page.getTexts(annotation.textBefore, annotation.textAfter);
                        if (!annotation.annotation.isEmpty()) {
                            page.marks.add(annotation);
                        }
                    }
                }
            }
        }
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.core.AbstractViewController#onLayoutChanged(boolean, boolean, android.graphics.Rect,
     * android.graphics.Rect)
     */
    @Override
    public final boolean onLayoutChanged(final boolean layoutChanged) {
        LOG.d("onLayoutChanged");
        final AppBook bs = SettingsManager.getBookSettings();
        final int page = model != null ? model.getCurrentViewPageIndex() : -1;
        final float offsetX = bs != null ? bs.x : 0;
        final float offsetY = bs != null ? bs.y : 0;

        if (super.onLayoutChanged(layoutChanged)) {
            if (isShown && layoutChanged && page != -1) {
                goToPage(page, offsetX, offsetY);
            }
            return true;
        }
        return false;
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#onScrollChanged(int, int)
     */
    @Override
    public final void onScrollChanged(final int dX, final int dY) {
        if (inZoom.get()) {
            return;
        }

        EventPool.newEventScroll(this, mode == DocumentViewMode.VERTICAL_SCROLL ? dY : dX).process();
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.core.AbstractViewController#isPageVisible(org.ebookdroid.core.Page,
     * org.ebookdroid.core.ViewState)
     */
    @Override
    public final boolean isPageVisible(final Page page, final ViewState viewState) {
        if (viewState.ctrl instanceof PageCurlController) {
            // 仿真翻页模式：只有当前页和相邻页可见
            Page currentPage = viewState.pages.getCurrentPage();
            if (currentPage == null) {
                LOG.d("测试仿真翻页", "当前页面为空");
                // 如果当前页为空，第一页总是可见的
                return page.index.docIndex == 0;
            }

            int currentPageIndex = currentPage.index.docIndex;
            int pageIndex = page.index.docIndex;
            boolean visible = Math.abs(pageIndex - currentPageIndex) <= 1;

            LOG.d("测试仿真翻页", "页面可见性检查", "页面", pageIndex, "当前", currentPageIndex, "可见", visible);
            return visible;
        }
        return RectF.intersects(viewState.viewRect, viewState.getBounds(page));
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#pageUpdated(org.ebookdroid.core.ViewState,
     * org.ebookdroid.core.Page)
     */
    @Override
    public void pageUpdated(final ViewState viewState, final Page page) {
    }

    @Override
    public void updateAnimation() {

    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#updateAnimationType()
     */
    @Override
    public void updateAnimationType() {

    }
}
