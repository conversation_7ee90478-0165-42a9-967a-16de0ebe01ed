package org.ebookdroid.core;

import android.graphics.RectF;

import com.tct.exbook.android.utils.LOG;
import com.tct.exbook.model.AppAnnotation;
import com.tct.exbook.model.AppBook;
import com.tct.exbook.pdf.info.AnnotationData;
import com.tct.exbook.pdf.info.model.BookCSS;

import org.ebookdroid.common.settings.SettingsManager;
import org.ebookdroid.common.settings.types.DocumentViewMode;
import org.ebookdroid.ui.viewer.IActivityController;

import java.util.List;

public abstract class AbstractScrollController extends AbstractViewController {


    protected AbstractScrollController(final IActivityController base, final DocumentViewMode mode) {
        super(base, mode);
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#goToPage(int)
     */
    @Override
    public ViewState goToPage(final int toPage) {
        return new EventGotoPage(this, toPage, false).process();
    }

    @Override
    public ViewState goToPageAndCenter(int page) {
        return new EventGotoPage(this, page, false, true).process();

    }

    @Override
    public final ViewState goToPage(final int toPage, boolean animate) {
        return new EventGotoPage(this, toPage, animate).process();
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#goToPage(int, float, float)
     */
    @Override
    public final ViewState goToPage(final int toPage, final float offsetX, final float offsetY) {
        return new EventGotoPage(this, toPage, offsetX, offsetY).process();
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#drawView(org.ebookdroid.core.EventDraw)
     */
    @Override
    public final void drawView(final EventDraw eventDraw) {
        final ViewState viewState = eventDraw.viewState;
        LOG.d("测试仿真翻页 ", "AbstractScrollController drawView ");
        if (viewState.model == null) {
            // 测试仿真翻页
            LOG.d("测试仿真翻页 ", "viewstate model == null");
            return;
        }

        LOG.d("测试仿真翻页", "pages", viewState.pages);
        LOG.d("测试仿真翻页", "model.pages.length", viewState.model.getPages().length);
        LOG.d("测试仿真翻页", "currentIndex", viewState.pages.currentIndex);

        if (viewState.ctrl instanceof PageCurlController) {
            // 处理仿真页面
            PageCurlController curlController = (PageCurlController) viewState.ctrl;
            drawCurlPages(eventDraw, viewState, curlController);
        } else {
            for (final Page page : viewState.pages.getVisiblePages()) {

                if (page != null) {
                    LOG.d("测试仿真翻页", page.toString());
                    loadAnnotations(page, viewState);
                    eventDraw.process(page);
                }
            }
        }
        getView().continueScroll();
    }

    /**
     * 绘制仿真翻页效果
     */
    private void drawCurlPages(EventDraw eventDraw, ViewState viewState, PageCurlController curlController) {
        Page currentPage = viewState.pages.getCurrentPage();
        if (currentPage == null) {
            return;
        }

        LOG.d("测试仿真翻页", "绘制仿真翻页|状态=" + curlController.getCurlState() + "|进度=" + curlController.getCurlProgress());

        PageCurlController.CurlState curlState = curlController.getCurlState();

        if (curlState == PageCurlController.CurlState.NONE) {
            // 无翻页动画，正常绘制当前页
            loadAnnotations(currentPage, viewState);
            eventDraw.process(currentPage);
        } else {
            // 有翻页动画，需要绘制当前页和目标页
            drawCurlAnimation(eventDraw, viewState, curlController, currentPage);
        }
    }

    /**
     * 绘制翻页动画
     */
    private void drawCurlAnimation(EventDraw eventDraw, ViewState viewState, PageCurlController curlController, Page currentPage) {
        PageCurlController.CurlDirection direction = curlController.getCurlDirection();
        float progress = curlController.getCurlProgress();

        // 获取目标页面
        int currentPageIndex = currentPage.index.docIndex;
        // 方向到目标页索引的映射：
        // RIGHT_TO_LEFT（向左拖） => 下一页；LEFT_TO_RIGHT（向右拖） => 上一页
        int targetPageIndex = direction == PageCurlController.CurlDirection.RIGHT_TO_LEFT ?
            currentPageIndex + 1 : currentPageIndex - 1;

        Page targetPage = null;
        if (targetPageIndex >= 0 && targetPageIndex < model.getPages().length) {
            targetPage = model.getPageByDocIndex(targetPageIndex);
        }

        // 保存画布状态
        eventDraw.canvas.save();

        try {
            if (direction == PageCurlController.CurlDirection.LEFT_TO_RIGHT) {
                // 从左到右翻页（上一页）
                drawLeftToRightCurl(eventDraw, viewState, currentPage, targetPage, progress);
            } else {
                // 从右到左翻页（下一页）
                drawRightToLeftCurl(eventDraw, viewState, currentPage, targetPage, progress);
            }
        } finally {
            // 恢复画布状态
            eventDraw.canvas.restore();
        }
    }

    /**
     * 绘制从左到右的翻页效果（上一页）
     */
    private void drawLeftToRightCurl(EventDraw eventDraw, ViewState viewState, Page currentPage, Page targetPage, float progress) {
        int width = getWidth();
        int height = getHeight();

        // 计算翻页区域（从左侧逐渐展开）
        float curlX = width * progress;

        // 先绘制当前页全屏，避免目标页尚未解码时出现整屏底色
        loadAnnotations(currentPage, viewState);
        eventDraw.process(currentPage);

        // 再只在左侧已展开的区域绘制目标页（上一页）
        if (targetPage != null && progress > 0f) {
            eventDraw.canvas.save();
            eventDraw.canvas.clipRect(0, 0, curlX, height);
            loadAnnotations(targetPage, viewState);
            eventDraw.process(targetPage);
            eventDraw.canvas.restore();
        }

        // 绘制卷曲区域（带渐变阴影）
        drawCurlFold(eventDraw, curlX, width, height, progress, true);
    }

    /**
     * 绘制从右到左的翻页效果（下一页）
     */
    private void drawRightToLeftCurl(EventDraw eventDraw, ViewState viewState, Page currentPage, Page targetPage, float progress) {
        int width = getWidth();
        int height = getHeight();

        // 计算翻页区域（从右侧逐渐展开）
        float curlX = width * (1.0f - progress);

        // 先绘制当前页全屏
        loadAnnotations(currentPage, viewState);
        eventDraw.process(currentPage);

        // 再在右侧已展开的区域绘制目标页（下一页）
        if (targetPage != null && progress > 0f) {
            eventDraw.canvas.save();
            eventDraw.canvas.clipRect(curlX, 0, width, height);
            loadAnnotations(targetPage, viewState);
            eventDraw.process(targetPage);
            eventDraw.canvas.restore();
        }

        // 绘制卷曲区域（带渐变阴影）
        drawCurlFold(eventDraw, curlX, width, height, progress, false);
    }

    /**
     * 绘制翻页阴影效果
     */
    private void drawCurlShadow(EventDraw eventDraw, float curlX, float curlY, int width, int height, float progress) {
        // 简单的阴影效果：在翻页边缘绘制渐变阴影
        android.graphics.Paint shadowPaint = new android.graphics.Paint();
        shadowPaint.setColor(android.graphics.Color.BLACK);
        shadowPaint.setAlpha((int) (50 * progress)); // 阴影透明度随进度变化

        // 绘制垂直阴影线
        float shadowWidth = 10 * progress;
        eventDraw.canvas.drawRect(curlX - shadowWidth, curlY, curlX, height, shadowPaint);
    }

    /**
     * 绘制卷曲区域（用三角区域+渐变阴影近似模拟页角卷曲）
     * @param fromLeft true 表示从左侧卷起（上一页），false 表示从右侧卷起（下一页）
     */
    private void drawCurlFold(EventDraw eventDraw, float curlX, int width, int height, float progress, boolean fromLeft) {
        // 卷曲深度随进度递减，避免一开始就太宽
        float maxDepth = width * 0.15f;
        float foldDepth = Math.max(8f, maxDepth * (1.0f - progress));

        // 构造卷曲三角形区域
        android.graphics.Path foldPath = new android.graphics.Path();
        foldPath.moveTo(curlX, 0);
        foldPath.lineTo(curlX, height);
        float tipX = fromLeft ? (curlX + foldDepth) : (curlX - foldDepth);
        float tipY = height * 0.5f;
        foldPath.lineTo(tipX, tipY);
        foldPath.close();

        // 阴影渐变（由深到浅），增强卷曲立体感
        float shadowStartX = curlX;
        float shadowEndX = tipX;
        android.graphics.LinearGradient gradient = new android.graphics.LinearGradient(
                shadowStartX, 0, shadowEndX, 0,
                new int[]{0x66000000, 0x22000000, 0x00000000},
                new float[]{0f, 0.6f, 1f},
                android.graphics.Shader.TileMode.CLAMP
        );
        android.graphics.Paint foldPaint = new android.graphics.Paint(android.graphics.Paint.ANTI_ALIAS_FLAG);
        foldPaint.setShader(gradient);

        eventDraw.canvas.drawPath(foldPath, foldPaint);

        // 在卷曲边缘绘制高光线，模拟纸张折痕
        android.graphics.Paint highlight = new android.graphics.Paint(android.graphics.Paint.ANTI_ALIAS_FLAG);
        highlight.setColor(0x88FFFFFF);
        highlight.setStrokeWidth(Math.max(2f, 6f * (1.0f - progress)));
        eventDraw.canvas.drawLine(curlX, 0, curlX, height, highlight);
    }



    /**
     * 加载标注
     *
     * @param page
     * @param viewState
     */
    private void loadAnnotations(Page page, ViewState viewState) {
        if (page.marks.isEmpty()) {
            if (page.texts == null) {
                page.texts = viewState.model.decodeService.getCodecDocument().getPage(page.index.docIndex).getText();
            }
            List<AppAnnotation> annotations = AnnotationData.get().getBookmarksByBook(viewState.book.path);
            // 查找标注位置
            for (AppAnnotation annotation : annotations) {
                LOG.d("测试框架", annotation.page, page.index.docIndex, page.index.viewIndex);
                LOG.d("测试删除 annotation", annotation.text);
                if (BookCSS.get().fontSizeSp == annotation.fontSize) {
                    if (annotation.page == page.index.docIndex) {
                        annotation.annotation = page.getTexts(annotation.startIndex, annotation.endIndex);
                        if (annotation.annotation.isEmpty()) {
                            annotation.annotation = page.getTexts(annotation.textBefore, annotation.textAfter);
                        }
                        if (!annotation.annotation.isEmpty()) {
                            page.marks.add(annotation);
                        }
                    }
                } else {
                    if (annotation.annotation.isEmpty()) {
                        annotation.annotation = page.getTexts(annotation.textBefore, annotation.textAfter);
                        if (!annotation.annotation.isEmpty()) {
                            page.marks.add(annotation);
                        }
                    }
                }
            }
        }
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.core.AbstractViewController#onLayoutChanged(boolean, boolean, android.graphics.Rect,
     * android.graphics.Rect)
     */
    @Override
    public final boolean onLayoutChanged(final boolean layoutChanged) {
        LOG.d("onLayoutChanged");
        final AppBook bs = SettingsManager.getBookSettings();
        final int page = model != null ? model.getCurrentViewPageIndex() : -1;
        final float offsetX = bs != null ? bs.x : 0;
        final float offsetY = bs != null ? bs.y : 0;

        if (super.onLayoutChanged(layoutChanged)) {
            if (isShown && layoutChanged && page != -1) {
                goToPage(page, offsetX, offsetY);
            }
            return true;
        }
        return false;
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#onScrollChanged(int, int)
     */
    @Override
    public final void onScrollChanged(final int dX, final int dY) {
        if (inZoom.get()) {
            return;
        }

        EventPool.newEventScroll(this, mode == DocumentViewMode.VERTICAL_SCROLL ? dY : dX).process();
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.core.AbstractViewController#isPageVisible(org.ebookdroid.core.Page,
     * org.ebookdroid.core.ViewState)
     */
    @Override
    public final boolean isPageVisible(final Page page, final ViewState viewState) {
        if (viewState.ctrl instanceof PageCurlController) {
            // 仿真翻页模式：只有当前页和相邻页可见
            Page currentPage = viewState.pages.getCurrentPage();
            if (currentPage == null) {
                LOG.d("测试仿真翻页", "当前页面为空");
                // 如果当前页为空，第一页总是可见的
                return page.index.docIndex == 0;
            }

            int currentPageIndex = currentPage.index.docIndex;
            int pageIndex = page.index.docIndex;
            boolean visible = Math.abs(pageIndex - currentPageIndex) <= 1;

            LOG.d("测试仿真翻页", "页面可见性检查", "页面", pageIndex, "当前", currentPageIndex, "可见", visible);
            return visible;
        }
        return RectF.intersects(viewState.viewRect, viewState.getBounds(page));
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#pageUpdated(org.ebookdroid.core.ViewState,
     * org.ebookdroid.core.Page)
     */
    @Override
    public void pageUpdated(final ViewState viewState, final Page page) {
    }

    @Override
    public void updateAnimation() {

    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#updateAnimationType()
     */
    @Override
    public void updateAnimationType() {

    }
}
