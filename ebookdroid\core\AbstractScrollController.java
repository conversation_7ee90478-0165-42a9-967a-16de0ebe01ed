package org.ebookdroid.core;

import android.graphics.RectF;

import com.tct.exbook.android.utils.LOG;
import com.tct.exbook.model.AppAnnotation;
import com.tct.exbook.model.AppBook;
import com.tct.exbook.pdf.info.AnnotationData;
import com.tct.exbook.pdf.info.model.BookCSS;

import org.ebookdroid.common.settings.SettingsManager;
import org.ebookdroid.common.settings.types.DocumentViewMode;
import org.ebookdroid.ui.viewer.IActivityController;

import java.util.List;

public abstract class AbstractScrollController extends AbstractViewController {


    protected AbstractScrollController(final IActivityController base, final DocumentViewMode mode) {
        super(base, mode);
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#goToPage(int)
     */
    @Override
    public ViewState goToPage(final int toPage) {
        return new EventGotoPage(this, toPage, false).process();
    }

    @Override
    public ViewState goToPageAndCenter(int page) {
        return new EventGotoPage(this, page, false, true).process();

    }

    @Override
    public final ViewState goToPage(final int toPage, boolean animate) {
        return new EventGotoPage(this, toPage, animate).process();
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#goToPage(int, float, float)
     */
    @Override
    public final ViewState goToPage(final int toPage, final float offsetX, final float offsetY) {
        return new EventGotoPage(this, toPage, offsetX, offsetY).process();
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#drawView(org.ebookdroid.core.EventDraw)
     */
    @Override
    public final void drawView(final EventDraw eventDraw) {
        final ViewState viewState = eventDraw.viewState;
        LOG.d("测试仿真翻页 ", "AbstractScrollController drawView ");
        if (viewState.model == null) {
            // 测试仿真翻页
            LOG.d("测试仿真翻页 ", "viewstate model == null");
            return;
        }

        LOG.d("测试仿真翻页", "pages", viewState.pages);
        LOG.d("测试仿真翻页", "model.pages.length", viewState.model.getPages().length);
        LOG.d("测试仿真翻页", "currentIndex", viewState.pages.currentIndex);

        if (viewState.ctrl instanceof PageCurlController) {
            // 处理仿真页面
            PageCurlController curlController = (PageCurlController) viewState.ctrl;
            drawCurlPages(eventDraw, viewState, curlController);
        } else {
            for (final Page page : viewState.pages.getVisiblePages()) {

                if (page != null) {
                    LOG.d("测试仿真翻页", page.toString());
                    loadAnnotations(page, viewState);
                    eventDraw.process(page);
                }
            }
        }
        getView().continueScroll();
    }

    /**
     * 绘制仿真翻页效果
     */
    private void drawCurlPages(EventDraw eventDraw, ViewState viewState, PageCurlController curlController) {
        Page currentPage = viewState.pages.getCurrentPage();
        if (currentPage == null) {
            return;
        }

        LOG.d("测试仿真翻页", "绘制仿真翻页|状态=" + curlController.getCurlState() + "|进度=" + curlController.getCurlProgress());

        PageCurlController.CurlState curlState = curlController.getCurlState();

        if (curlState == PageCurlController.CurlState.NONE) {
            // 无翻页动画，正常绘制当前页
            loadAnnotations(currentPage, viewState);
            eventDraw.process(currentPage);
        } else {
            // 有翻页动画，需要绘制当前页和目标页
            drawCurlAnimation(eventDraw, viewState, curlController, currentPage);
        }
    }

    /**
     * 绘制翻页动画
     */
    private void drawCurlAnimation(EventDraw eventDraw, ViewState viewState, PageCurlController curlController, Page currentPage) {
        PageCurlController.CurlDirection direction = curlController.getCurlDirection();
        float progress = curlController.getCurlProgress();

        // 获取目标页面
        int currentPageIndex = currentPage.index.docIndex;
        // 方向到目标页索引的映射：
        // RIGHT_TO_LEFT（向左拖） => 下一页；LEFT_TO_RIGHT（向右拖） => 上一页
        int targetPageIndex = direction == PageCurlController.CurlDirection.RIGHT_TO_LEFT ?
            currentPageIndex + 1 : currentPageIndex - 1;

        Page targetPage = null;
        if (targetPageIndex >= 0 && targetPageIndex < model.getPages().length) {
            targetPage = model.getPageByDocIndex(targetPageIndex);
        }

        // 保存画布状态
        eventDraw.canvas.save();

        try {
            if (direction == PageCurlController.CurlDirection.LEFT_TO_RIGHT) {
                // 从左到右翻页（上一页）
                drawLeftToRightCurl(eventDraw, viewState, currentPage, targetPage, progress);
            } else {
                // 从右到左翻页（下一页）
                drawRightToLeftCurl(eventDraw, viewState, currentPage, targetPage, progress);
            }
        } finally {
            // 恢复画布状态
            eventDraw.canvas.restore();
        }
    }

    /**
     * 绘制从左到右的翻页效果（上一页）
     */
    private void drawLeftToRightCurl(EventDraw eventDraw, ViewState viewState, Page currentPage, Page targetPage, float progress) {
        int width = getWidth();
        int height = getHeight();

        // 计算翻页区域（从左侧逐渐展开）
        float curlX = width * progress;

        // 先绘制目标页（上一页）作为底层
        if (targetPage != null) {
            loadAnnotations(targetPage, viewState);
            eventDraw.process(targetPage);
        }

        // 绘制当前页的可见部分（右侧未被卷起的部分）
        if (progress < 1.0f) {
            eventDraw.canvas.save();
            eventDraw.canvas.clipRect(curlX, 0, width, height);
            loadAnnotations(currentPage, viewState);
            eventDraw.process(currentPage);
            eventDraw.canvas.restore();
        }

        // 绘制卷曲的当前页部分（带镜像反射）
        if (progress > 0f && currentPage != null) {
            drawCurlPageContent(eventDraw, viewState, currentPage, curlX, width, height, progress, true);
        }

        // 绘制卷曲区域的3D效果
        drawCurlFold(eventDraw, curlX, width, height, progress, true);
    }

    /**
     * 绘制从右到左的翻页效果（下一页）
     */
    private void drawRightToLeftCurl(EventDraw eventDraw, ViewState viewState, Page currentPage, Page targetPage, float progress) {
        int width = getWidth();
        int height = getHeight();

        // 计算翻页区域（从右侧逐渐展开）
        float curlX = width * (1.0f - progress);

        // 先绘制目标页（下一页）作为底层
        if (targetPage != null) {
            loadAnnotations(targetPage, viewState);
            eventDraw.process(targetPage);
        }

        // 绘制当前页的可见部分（左侧未被卷起的部分）
        if (progress < 1.0f) {
            eventDraw.canvas.save();
            eventDraw.canvas.clipRect(0, 0, curlX, height);
            loadAnnotations(currentPage, viewState);
            eventDraw.process(currentPage);
            eventDraw.canvas.restore();
        }

        // 绘制卷曲的当前页部分（带镜像反射）
        if (progress > 0f && currentPage != null) {
            drawCurlPageContent(eventDraw, viewState, currentPage, curlX, width, height, progress, false);
        }

        // 绘制卷曲区域的3D效果
        drawCurlFold(eventDraw, curlX, width, height, progress, false);
    }

    /**
     * 绘制卷曲页面内容（带镜像反射效果）
     */
    private void drawCurlPageContent(EventDraw eventDraw, ViewState viewState, Page page,
                                    float curlX, int width, int height, float progress, boolean fromLeft) {
        android.graphics.Canvas canvas = eventDraw.canvas;

        // 计算卷曲区域的宽度
        float curlWidth = fromLeft ? (width - curlX) : curlX;
        if (curlWidth <= 10) return; // 太小的区域不处理

        try {
            // 创建位图来渲染被卷曲的页面部分
            int bitmapWidth = Math.max(10, (int)curlWidth);
            android.graphics.Bitmap curlBitmap = android.graphics.Bitmap.createBitmap(
                    bitmapWidth, height, android.graphics.Bitmap.Config.ARGB_8888);
            android.graphics.Canvas curlCanvas = new android.graphics.Canvas(curlBitmap);

            // 渲染被卷曲部分的页面内容
            android.graphics.Matrix pageMatrix = new android.graphics.Matrix();
            if (fromLeft) {
                // 从左侧卷起：渲染页面左侧部分（被卷起的部分）
                pageMatrix.setTranslate(0, 0);
                curlCanvas.clipRect(0, 0, curlWidth, height);
            } else {
                // 从右侧卷起：渲染页面右侧部分（被卷起的部分）
                pageMatrix.setTranslate(-curlX, 0);
                curlCanvas.clipRect(0, 0, curlWidth, height);
            }
            curlCanvas.setMatrix(pageMatrix);

            // 创建临时EventDraw来渲染页面内容
            EventDraw tempEventDraw = new EventDraw(null);
            tempEventDraw.init(viewState, curlCanvas, base);
            loadAnnotations(page, viewState);
            tempEventDraw.process(page);

            // 计算卷曲参数
            float curlAngle = progress * 45f; // 最大45度卷曲
            float curlRadius = curlWidth * 0.5f;

            // 绘制卷曲的正面
            canvas.save();
            android.graphics.Matrix frontMatrix = new android.graphics.Matrix();

            if (fromLeft) {
                // 从左侧卷起：正面在左侧，逐渐向右卷曲
                frontMatrix.setTranslate(0, 0);
                frontMatrix.postSkew(progress * 0.5f, 0); // 向右倾斜
                frontMatrix.postScale(1.0f - progress * 0.3f, 1.0f); // 水平压缩
            } else {
                // 从右侧卷起：正面在右侧，逐渐向左卷曲
                frontMatrix.setTranslate(curlX, 0);
                frontMatrix.postSkew(-progress * 0.5f, 0); // 向左倾斜
                frontMatrix.postScale(1.0f - progress * 0.3f, 1.0f); // 水平压缩
            }

            canvas.setMatrix(frontMatrix);

            android.graphics.Paint frontPaint = new android.graphics.Paint(android.graphics.Paint.ANTI_ALIAS_FLAG);
            frontPaint.setAlpha((int)(255 * (1.0f - progress * 0.4f))); // 随卷曲变暗
            canvas.drawBitmap(curlBitmap, 0, 0, frontPaint);
            canvas.restore();

            // 绘制卷曲的背面（镜像）
            if (progress > 0.3f) {
                canvas.save();
                android.graphics.Matrix backMatrix = new android.graphics.Matrix();

                if (fromLeft) {
                    // 从左侧卷起：背面镜像显示在卷曲区域的右侧
                    float backX = curlWidth * (1.0f - progress * 0.7f);
                    backMatrix.setScale(-1.0f, 1.0f); // 水平镜像
                    backMatrix.postTranslate(backX + curlWidth, 0);
                    backMatrix.postSkew(-progress * 0.3f, 0); // 反向倾斜
                    backMatrix.postScale(0.7f + progress * 0.2f, 1.0f);
                } else {
                    // 从右侧卷起：背面镜像显示在卷曲区域的左侧
                    float backX = curlX + curlWidth * progress * 0.7f;
                    backMatrix.setScale(-1.0f, 1.0f); // 水平镜像
                    backMatrix.postTranslate(backX, 0);
                    backMatrix.postSkew(progress * 0.3f, 0); // 反向倾斜
                    backMatrix.postScale(0.7f + progress * 0.2f, 1.0f);
                }

                canvas.setMatrix(backMatrix);

                android.graphics.Paint backPaint = new android.graphics.Paint(android.graphics.Paint.ANTI_ALIAS_FLAG);
                backPaint.setAlpha((int)(120 * (progress - 0.3f) / 0.7f)); // 背面更暗

                // 背面色调调整（偏灰暗）
                android.graphics.ColorMatrix colorMatrix = new android.graphics.ColorMatrix();
                colorMatrix.setSaturation(0.2f); // 大幅降低饱和度
                colorMatrix.postConcat(new android.graphics.ColorMatrix(new float[]{
                    0.6f, 0, 0, 0, 0,    // 大幅降低红色
                    0, 0.6f, 0, 0, 0,    // 大幅降低绿色
                    0, 0, 0.6f, 0, 0,    // 大幅降低蓝色
                    0, 0, 0, 1, 0        // 保持透明度
                }));
                backPaint.setColorFilter(new android.graphics.ColorMatrixColorFilter(colorMatrix));

                canvas.drawBitmap(curlBitmap, 0, 0, backPaint);
                canvas.restore();
            }

            // 清理资源
            curlBitmap.recycle();

        } catch (Exception e) {
            // 如果位图创建失败，回退到简单绘制
            LOG.e("测试仿真翻页", "绘制卷曲页面内容失败: " + e.getMessage());
        }
    }
    /**
     * 绘制翻页阴影效果
     */
    private void drawCurlShadow(EventDraw eventDraw, float curlX, float curlY, int width, int height, float progress) {
        // 简单的阴影效果：在翻页边缘绘制渐变阴影
        android.graphics.Paint shadowPaint = new android.graphics.Paint();
        shadowPaint.setColor(android.graphics.Color.BLACK);
        shadowPaint.setAlpha((int) (50 * progress)); // 阴影透明度随进度变化

        // 绘制垂直阴影线
        float shadowWidth = 10 * progress;
        eventDraw.canvas.drawRect(curlX - shadowWidth, curlY, curlX, height, shadowPaint);
    }

    /**
     * 绘制拟真纸张卷曲效果（贝塞尔曲线+镜像反射+背面着色）
     * @param fromLeft true 表示从左侧卷起（上一页），false 表示从右侧卷起（下一页）
     */
    private void drawCurlFold(EventDraw eventDraw, float curlX, int width, int height, float progress, boolean fromLeft) {
        android.graphics.Canvas canvas = eventDraw.canvas;

        // 计算卷曲参数
        float maxCurlWidth = width * 0.15f; // 减小最大卷曲宽度，让效果更自然
        float curlWidth = Math.max(8f, maxCurlWidth * progress); // 卷曲宽度随进度增加

        // 只有足够的卷曲进度才绘制卷曲效果
        if (progress < 0.05f || curlWidth < 8f) return;

        // 绘制卷曲的各个部分
        drawCurlFrontSurface(canvas, curlX, curlWidth, height, progress, fromLeft);
        drawCurlBackSurface(canvas, curlX, curlWidth, height, progress, fromLeft);
        drawCurlShadowAndHighlight(canvas, curlX, curlWidth, height, progress, fromLeft);
    }

    /**
     * 绘制卷曲前表面（正在被翻起的页面部分）
     */
    private void drawCurlFrontSurface(android.graphics.Canvas canvas, float curlX, float curlWidth,
                                     int height, float progress, boolean fromLeft) {
        // 创建卷曲表面路径
        android.graphics.Path curlPath = new android.graphics.Path();

        // 计算卷曲的关键点
        float curlDepth = curlWidth * 0.8f; // 卷曲深度
        float tipX, tipY;

        if (fromLeft) {
            // 从左侧卷起：卷曲向右延伸
            tipX = curlX + curlDepth;
            tipY = height * 0.5f;

            // 创建卷曲表面路径（三角形区域）
            curlPath.moveTo(curlX, 0);
            curlPath.lineTo(tipX, tipY);
            curlPath.lineTo(curlX, height);
            curlPath.close();
        } else {
            // 从右侧卷起：卷曲向左延伸
            tipX = curlX - curlDepth;
            tipY = height * 0.5f;

            // 创建卷曲表面路径（三角形区域）
            curlPath.moveTo(curlX, 0);
            curlPath.lineTo(tipX, tipY);
            curlPath.lineTo(curlX, height);
            curlPath.close();
        }

        // 创建卷曲表面的渐变（模拟光照）
        float gradientStart = curlX;
        float gradientEnd = tipX;

        android.graphics.LinearGradient surfaceGradient = new android.graphics.LinearGradient(
                gradientStart, 0, gradientEnd, 0,
                new int[]{0x00FFFFFF, 0x40FFFFFF, 0x80FFFFFF},
                new float[]{0f, 0.5f, 1f},
                android.graphics.Shader.TileMode.CLAMP
        );

        android.graphics.Paint surfacePaint = new android.graphics.Paint(android.graphics.Paint.ANTI_ALIAS_FLAG);
        surfacePaint.setShader(surfaceGradient);
        surfacePaint.setAlpha((int)(150 * progress));

        canvas.drawPath(curlPath, surfacePaint);

        // 绘制卷曲边缘的高光线
        android.graphics.Paint edgePaint = new android.graphics.Paint(android.graphics.Paint.ANTI_ALIAS_FLAG);
        edgePaint.setColor(0xFFFFFFFF);
        edgePaint.setAlpha((int)(200 * progress));
        edgePaint.setStrokeWidth(Math.max(1f, 3f * progress));
        edgePaint.setStyle(android.graphics.Paint.Style.STROKE);

        canvas.drawLine(curlX, 0, curlX, height, edgePaint);
    }

    /**
     * 绘制卷曲背面（镜像反射效果）
     */
    private void drawCurlBackSurface(android.graphics.Canvas canvas, float curlX, float curlWidth,
                                    int height, float progress, boolean fromLeft) {
        if (progress < 0.2f) return; // 卷曲不够时不显示背面

        // 计算背面参数
        float backIntensity = (progress - 0.2f) / 0.8f; // 背面显示强度
        float backDepth = curlWidth * 0.5f;
        float tipX = fromLeft ? (curlX + curlWidth * 0.8f) : (curlX - curlWidth * 0.8f);

        // 创建背面路径（简化的三角形）
        android.graphics.Path backPath = new android.graphics.Path();

        if (fromLeft) {
            // 从左侧卷起：背面在卷曲尖端的右侧
            float backTipX = tipX + backDepth * backIntensity;
            backPath.moveTo(tipX, height * 0.2f);
            backPath.lineTo(backTipX, height * 0.5f);
            backPath.lineTo(tipX, height * 0.8f);
            backPath.close();
        } else {
            // 从右侧卷起：背面在卷曲尖端的左侧
            float backTipX = tipX - backDepth * backIntensity;
            backPath.moveTo(tipX, height * 0.2f);
            backPath.lineTo(backTipX, height * 0.5f);
            backPath.lineTo(tipX, height * 0.8f);
            backPath.close();
        }

        // 背面渐变（暗色）
        android.graphics.Paint backPaint = new android.graphics.Paint(android.graphics.Paint.ANTI_ALIAS_FLAG);
        backPaint.setColor(0xFF606060);
        backPaint.setAlpha((int)(100 * backIntensity));

        canvas.drawPath(backPath, backPaint);

        // 背面边缘线
        android.graphics.Paint backEdgePaint = new android.graphics.Paint(android.graphics.Paint.ANTI_ALIAS_FLAG);
        backEdgePaint.setColor(0xFF404040);
        backEdgePaint.setAlpha((int)(150 * backIntensity));
        backEdgePaint.setStrokeWidth(1f);
        backEdgePaint.setStyle(android.graphics.Paint.Style.STROKE);

        canvas.drawPath(backPath, backEdgePaint);
    }

    /**
     * 绘制卷曲阴影和高光效果
     */
    private void drawCurlShadowAndHighlight(android.graphics.Canvas canvas, float curlX, float curlWidth,
                                           int height, float progress, boolean fromLeft) {
        // 1. 绘制主折痕阴影
        android.graphics.Paint shadowPaint = new android.graphics.Paint(android.graphics.Paint.ANTI_ALIAS_FLAG);
        shadowPaint.setColor(0xFF000000);
        shadowPaint.setAlpha((int)(60 * progress));
        shadowPaint.setStrokeWidth(2f);

        canvas.drawLine(curlX, 0, curlX, height, shadowPaint);

        // 2. 绘制卷曲区域的投影
        float shadowWidth = curlWidth * 0.5f;
        android.graphics.RectF shadowRect;

        if (fromLeft) {
            shadowRect = new android.graphics.RectF(curlX - shadowWidth, 0, curlX, height);
        } else {
            shadowRect = new android.graphics.RectF(curlX, 0, curlX + shadowWidth, height);
        }

        android.graphics.LinearGradient dropShadow = new android.graphics.LinearGradient(
                shadowRect.left, 0, shadowRect.right, 0,
                new int[]{0x00000000, 0x40000000},
                new float[]{0f, 1f},
                android.graphics.Shader.TileMode.CLAMP
        );

        android.graphics.Paint dropShadowPaint = new android.graphics.Paint(android.graphics.Paint.ANTI_ALIAS_FLAG);
        dropShadowPaint.setShader(dropShadow);
        dropShadowPaint.setAlpha((int)(100 * progress));

        canvas.drawRect(shadowRect, dropShadowPaint);
    }





    /**
     * 加载标注
     *
     * @param page
     * @param viewState
     */
    private void loadAnnotations(Page page, ViewState viewState) {
        if (page.marks.isEmpty()) {
            if (page.texts == null) {
                page.texts = viewState.model.decodeService.getCodecDocument().getPage(page.index.docIndex).getText();
            }
            List<AppAnnotation> annotations = AnnotationData.get().getBookmarksByBook(viewState.book.path);
            // 查找标注位置
            for (AppAnnotation annotation : annotations) {
                LOG.d("测试框架", annotation.page, page.index.docIndex, page.index.viewIndex);
                LOG.d("测试删除 annotation", annotation.text);
                if (BookCSS.get().fontSizeSp == annotation.fontSize) {
                    if (annotation.page == page.index.docIndex) {
                        annotation.annotation = page.getTexts(annotation.startIndex, annotation.endIndex);
                        if (annotation.annotation.isEmpty()) {
                            annotation.annotation = page.getTexts(annotation.textBefore, annotation.textAfter);
                        }
                        if (!annotation.annotation.isEmpty()) {
                            page.marks.add(annotation);
                        }
                    }
                } else {
                    if (annotation.annotation.isEmpty()) {
                        annotation.annotation = page.getTexts(annotation.textBefore, annotation.textAfter);
                        if (!annotation.annotation.isEmpty()) {
                            page.marks.add(annotation);
                        }
                    }
                }
            }
        }
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.core.AbstractViewController#onLayoutChanged(boolean, boolean, android.graphics.Rect,
     * android.graphics.Rect)
     */
    @Override
    public final boolean onLayoutChanged(final boolean layoutChanged) {
        LOG.d("onLayoutChanged");
        final AppBook bs = SettingsManager.getBookSettings();
        final int page = model != null ? model.getCurrentViewPageIndex() : -1;
        final float offsetX = bs != null ? bs.x : 0;
        final float offsetY = bs != null ? bs.y : 0;

        if (super.onLayoutChanged(layoutChanged)) {
            if (isShown && layoutChanged && page != -1) {
                goToPage(page, offsetX, offsetY);
            }
            return true;
        }
        return false;
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#onScrollChanged(int, int)
     */
    @Override
    public final void onScrollChanged(final int dX, final int dY) {
        if (inZoom.get()) {
            return;
        }

        EventPool.newEventScroll(this, mode == DocumentViewMode.VERTICAL_SCROLL ? dY : dX).process();
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.core.AbstractViewController#isPageVisible(org.ebookdroid.core.Page,
     * org.ebookdroid.core.ViewState)
     */
    @Override
    public final boolean isPageVisible(final Page page, final ViewState viewState) {
        if (viewState.ctrl instanceof PageCurlController) {
            // 仿真翻页模式：只有当前页和相邻页可见
            Page currentPage = viewState.pages.getCurrentPage();
            if (currentPage == null) {
                LOG.d("测试仿真翻页", "当前页面为空");
                // 如果当前页为空，第一页总是可见的
                return page.index.docIndex == 0;
            }

            int currentPageIndex = currentPage.index.docIndex;
            int pageIndex = page.index.docIndex;
            boolean visible = Math.abs(pageIndex - currentPageIndex) <= 1;

            LOG.d("测试仿真翻页", "页面可见性检查", "页面", pageIndex, "当前", currentPageIndex, "可见", visible);
            return visible;
        }
        return RectF.intersects(viewState.viewRect, viewState.getBounds(page));
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#pageUpdated(org.ebookdroid.core.ViewState,
     * org.ebookdroid.core.Page)
     */
    @Override
    public void pageUpdated(final ViewState viewState, final Page page) {
    }

    @Override
    public void updateAnimation() {

    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#updateAnimationType()
     */
    @Override
    public void updateAnimationType() {

    }
}
