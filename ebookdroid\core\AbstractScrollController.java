package org.ebookdroid.core;

import android.graphics.RectF;

import com.tct.exbook.android.utils.LOG;
import com.tct.exbook.model.AppAnnotation;
import com.tct.exbook.model.AppBook;
import com.tct.exbook.pdf.info.AnnotationData;
import com.tct.exbook.pdf.info.model.BookCSS;

import org.ebookdroid.common.settings.SettingsManager;
import org.ebookdroid.common.settings.types.DocumentViewMode;
import org.ebookdroid.ui.viewer.IActivityController;

import java.util.List;

public abstract class AbstractScrollController extends AbstractViewController {


    protected AbstractScrollController(final IActivityController base, final DocumentViewMode mode) {
        super(base, mode);
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#goToPage(int)
     */
    @Override
    public ViewState goToPage(final int toPage) {
        return new EventGotoPage(this, toPage, false).process();
    }

    @Override
    public ViewState goToPageAndCenter(int page) {
        return new EventGotoPage(this, page, false, true).process();

    }

    @Override
    public final ViewState goToPage(final int toPage, boolean animate) {
        return new EventGotoPage(this, toPage, animate).process();
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#goToPage(int, float, float)
     */
    @Override
    public final ViewState goToPage(final int toPage, final float offsetX, final float offsetY) {
        return new EventGotoPage(this, toPage, offsetX, offsetY).process();
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#drawView(org.ebookdroid.core.EventDraw)
     */
    @Override
    public final void drawView(final EventDraw eventDraw) {
        final ViewState viewState = eventDraw.viewState;
        LOG.d("测试仿真翻页 ", "AbstractScrollController drawView ");
        if (viewState.model == null) {
            // 测试仿真翻页
            LOG.d("测试仿真翻页 ", "viewstate model == null");
            return;
        }

        LOG.d("测试仿真翻页", "pages", viewState.pages);
        LOG.d("测试仿真翻页", "model.pages.length", viewState.model.getPages().length);
        LOG.d("测试仿真翻页", "currentIndex", viewState.pages.currentIndex);

        if (viewState.ctrl instanceof PageCurlController) {
            // 处理仿真页面
            PageCurlController curlController = (PageCurlController) viewState.ctrl;
            drawCurlPages(eventDraw, viewState, curlController);
        } else {
            for (final Page page : viewState.pages.getVisiblePages()) {

                if (page != null) {
                    LOG.d("测试仿真翻页", page.toString());
                    loadAnnotations(page, viewState);
                    eventDraw.process(page);
                }
            }
        }
        getView().continueScroll();
    }

    /**
     * 绘制仿真翻页效果
     */
    private void drawCurlPages(EventDraw eventDraw, ViewState viewState, PageCurlController curlController) {
        Page currentPage = viewState.pages.getCurrentPage();
        if (currentPage == null) {
            return;
        }

        LOG.d("测试仿真翻页", "绘制仿真翻页|状态=" + curlController.getCurlState() + "|进度=" + curlController.getCurlProgress());

        PageCurlController.CurlState curlState = curlController.getCurlState();

        if (curlState == PageCurlController.CurlState.NONE) {
            // 无翻页动画，正常绘制当前页
            loadAnnotations(currentPage, viewState);
            eventDraw.process(currentPage);
        } else {
            // 有翻页动画，需要绘制当前页和目标页
            drawCurlAnimation(eventDraw, viewState, curlController, currentPage);
        }
    }

    /**
     * 绘制翻页动画
     */
    private void drawCurlAnimation(EventDraw eventDraw, ViewState viewState, PageCurlController curlController, Page currentPage) {
        PageCurlController.CurlDirection direction = curlController.getCurlDirection();
        float progress = curlController.getCurlProgress();

        // 获取目标页面
        int currentPageIndex = currentPage.index.docIndex;
        int targetPageIndex = direction == PageCurlController.CurlDirection.LEFT_TO_RIGHT ?
            currentPageIndex + 1 : currentPageIndex - 1;

        Page targetPage = null;
        if (targetPageIndex >= 0 && targetPageIndex < model.getPages().length) {
            targetPage = model.getPageByDocIndex(targetPageIndex);
        }

        // 保存画布状态
        eventDraw.canvas.save();

        try {
            if (direction == PageCurlController.CurlDirection.LEFT_TO_RIGHT) {
                // 从左到右翻页（下一页）
                drawLeftToRightCurl(eventDraw, viewState, currentPage, targetPage, progress);
            } else {
                // 从右到左翻页（上一页）
                drawRightToLeftCurl(eventDraw, viewState, currentPage, targetPage, progress);
            }
        } finally {
            // 恢复画布状态
            eventDraw.canvas.restore();
        }
    }

    /**
     * 绘制从左到右的翻页效果（下一页）
     */
    private void drawLeftToRightCurl(EventDraw eventDraw, ViewState viewState, Page currentPage, Page targetPage, float progress) {
        int width = getWidth();
        int height = getHeight();

        // 计算翻页区域
        float curlX = width * progress;

        // 绘制目标页面（下一页）
        if (targetPage != null) {
            loadAnnotations(targetPage, viewState);
            eventDraw.process(targetPage);
        }

        // 绘制当前页面的可见部分
        if (progress < 1.0f) {
            eventDraw.canvas.save();
            // 裁剪当前页面的可见区域
            eventDraw.canvas.clipRect(curlX, 0, width, height);
            loadAnnotations(currentPage, viewState);
            eventDraw.process(currentPage);
            eventDraw.canvas.restore();
        }

        // 绘制翻页阴影效果
        drawCurlShadow(eventDraw, curlX, 0, width, height, progress);
    }

    /**
     * 绘制从右到左的翻页效果（上一页）
     */
    private void drawRightToLeftCurl(EventDraw eventDraw, ViewState viewState, Page currentPage, Page targetPage, float progress) {
        int width = getWidth();
        int height = getHeight();

        // 计算翻页区域
        float curlX = width * (1.0f - progress);

        // 绘制目标页面（上一页）
        if (targetPage != null) {
            loadAnnotations(targetPage, viewState);
            eventDraw.process(targetPage);
        }

        // 绘制当前页面的可见部分
        if (progress < 1.0f) {
            eventDraw.canvas.save();
            // 裁剪当前页面的可见区域
            eventDraw.canvas.clipRect(0, 0, curlX, height);
            loadAnnotations(currentPage, viewState);
            eventDraw.process(currentPage);
            eventDraw.canvas.restore();
        }

        // 绘制翻页阴影效果
        drawCurlShadow(eventDraw, curlX, 0, width, height, progress);
    }

    /**
     * 绘制翻页阴影效果
     */
    private void drawCurlShadow(EventDraw eventDraw, float curlX, float curlY, int width, int height, float progress) {
        // 简单的阴影效果：在翻页边缘绘制渐变阴影
        android.graphics.Paint shadowPaint = new android.graphics.Paint();
        shadowPaint.setColor(android.graphics.Color.BLACK);
        shadowPaint.setAlpha((int) (50 * progress)); // 阴影透明度随进度变化

        // 绘制垂直阴影线
        float shadowWidth = 10 * progress;
        eventDraw.canvas.drawRect(curlX - shadowWidth, curlY, curlX, height, shadowPaint);
    }


    /**
     * 加载标注
     *
     * @param page
     * @param viewState
     */
    private void loadAnnotations(Page page, ViewState viewState) {
        if (page.marks.isEmpty()) {
            if (page.texts == null) {
                page.texts = viewState.model.decodeService.getCodecDocument().getPage(page.index.docIndex).getText();
            }
            List<AppAnnotation> annotations = AnnotationData.get().getBookmarksByBook(viewState.book.path);
            // 查找标注位置
            for (AppAnnotation annotation : annotations) {
                LOG.d("测试框架", annotation.page, page.index.docIndex, page.index.viewIndex);
                LOG.d("测试删除 annotation", annotation.text);
                if (BookCSS.get().fontSizeSp == annotation.fontSize) {
                    if (annotation.page == page.index.docIndex) {
                        annotation.annotation = page.getTexts(annotation.startIndex, annotation.endIndex);
                        if (annotation.annotation.isEmpty()) {
                            annotation.annotation = page.getTexts(annotation.textBefore, annotation.textAfter);
                        }
                        if (!annotation.annotation.isEmpty()) {
                            page.marks.add(annotation);
                        }
                    }
                } else {
                    if (annotation.annotation.isEmpty()) {
                        annotation.annotation = page.getTexts(annotation.textBefore, annotation.textAfter);
                        if (!annotation.annotation.isEmpty()) {
                            page.marks.add(annotation);
                        }
                    }
                }
            }
        }
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.core.AbstractViewController#onLayoutChanged(boolean, boolean, android.graphics.Rect,
     * android.graphics.Rect)
     */
    @Override
    public final boolean onLayoutChanged(final boolean layoutChanged) {
        LOG.d("onLayoutChanged");
        final AppBook bs = SettingsManager.getBookSettings();
        final int page = model != null ? model.getCurrentViewPageIndex() : -1;
        final float offsetX = bs != null ? bs.x : 0;
        final float offsetY = bs != null ? bs.y : 0;

        if (super.onLayoutChanged(layoutChanged)) {
            if (isShown && layoutChanged && page != -1) {
                goToPage(page, offsetX, offsetY);
            }
            return true;
        }
        return false;
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#onScrollChanged(int, int)
     */
    @Override
    public final void onScrollChanged(final int dX, final int dY) {
        if (inZoom.get()) {
            return;
        }

        EventPool.newEventScroll(this, mode == DocumentViewMode.VERTICAL_SCROLL ? dY : dX).process();
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.core.AbstractViewController#isPageVisible(org.ebookdroid.core.Page,
     * org.ebookdroid.core.ViewState)
     */
    @Override
    public final boolean isPageVisible(final Page page, final ViewState viewState) {
        if (viewState.ctrl instanceof PageCurlController) {
            // 仿真翻页模式：只有当前页和相邻页可见
            Page currentPage = viewState.pages.getCurrentPage();
            if (currentPage == null) {
                LOG.d("测试仿真翻页", "当前页面为空");
                // 如果当前页为空，第一页总是可见的
                return page.index.docIndex == 0;
            }

            int currentPageIndex = currentPage.index.docIndex;
            int pageIndex = page.index.docIndex;
            boolean visible = Math.abs(pageIndex - currentPageIndex) <= 1;

            LOG.d("测试仿真翻页", "页面可见性检查", "页面", pageIndex, "当前", currentPageIndex, "可见", visible);
            return visible;
        }
        return RectF.intersects(viewState.viewRect, viewState.getBounds(page));
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#pageUpdated(org.ebookdroid.core.ViewState,
     * org.ebookdroid.core.Page)
     */
    @Override
    public void pageUpdated(final ViewState viewState, final Page page) {
    }

    @Override
    public void updateAnimation() {

    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#updateAnimationType()
     */
    @Override
    public void updateAnimationType() {

    }
}
