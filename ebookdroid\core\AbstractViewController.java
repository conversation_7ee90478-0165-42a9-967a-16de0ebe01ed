package org.ebookdroid.core;

import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.RectF;
import android.view.GestureDetector.SimpleOnGestureListener;
import android.view.MotionEvent;

import com.tct.exbook.android.utils.LOG;
import com.tct.exbook.android.utils.TxtUtils;
import com.tct.exbook.model.AppAnnotation;
import com.tct.exbook.model.AppBook;
import com.tct.exbook.model.AppState;
import com.tct.exbook.R;
import com.tct.exbook.pdf.info.model.BookCSS;
import com.tct.exbook.pdf.info.view.AlertDialogs;
import com.tct.exbook.pdf.info.view.DragingDialogs;
import com.tct.exbook.sys.AdvGuestureDetector;
import com.tct.exbook.sys.TempHolder;

import org.ebookdroid.common.settings.SettingsManager;
import org.ebookdroid.common.settings.types.DocumentViewMode;
import org.ebookdroid.common.settings.types.PageAlign;
import org.ebookdroid.common.settings.types.PageType;
import org.ebookdroid.common.touch.DefaultGestureDetector;
import org.ebookdroid.common.touch.IGestureDetector;
import org.ebookdroid.common.touch.IMultiTouchListener;
import org.ebookdroid.common.touch.MultiTouchGestureDetector;
import org.ebookdroid.common.touch.TouchManager;
import org.ebookdroid.common.touch.TouchManager.Touch;
import org.ebookdroid.core.codec.Annotation;
import org.ebookdroid.core.codec.PageLink;
import org.ebookdroid.core.models.DocumentModel;
import org.ebookdroid.droids.mupdf.codec.TextWord;
import org.ebookdroid.ui.viewer.IActivityController;
import org.ebookdroid.ui.viewer.IView;
import org.ebookdroid.ui.viewer.IViewController;
import org.emdev.ui.actions.AbstractComponentController;
import org.emdev.ui.actions.ActionEx;
import org.emdev.ui.actions.params.Constant;
import org.emdev.ui.progress.IProgressIndicator;
import org.emdev.utils.LengthUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

public abstract class AbstractViewController extends AbstractComponentController<IView> implements IViewController {


    public final IActivityController base;

    public final DocumentModel model;

    public final DocumentViewMode mode;
    protected final AtomicBoolean inZoom = new AtomicBoolean();
    protected final AtomicBoolean inQuickZoom = new AtomicBoolean();
    protected volatile boolean isInitialized = false;
    protected boolean isShown = false;

    //protected final PageIndex pageToGo;
    protected int firstVisiblePage;

    protected int lastVisiblePage;

    protected boolean layoutLocked;
    float xLong;
    float yLong;
    private List<IGestureDetector> detectors;

    public AbstractViewController(final IActivityController base, final DocumentViewMode mode) {
        super(base, base.getView());

        this.base = base;
        this.mode = mode;
        this.model = base.getDocumentModel();

        this.firstVisiblePage = -1;
        this.lastVisiblePage = -1;

        //this.pageToGo = SettingsManager.getBookSettings().getCurrentPage(base.getDocumentModel().getPageCount());

//        createAction(R.id.adFrame, new Constant("direction", -1));
//        createAction(R.id.adFrame, new Constant("direction", +1));

    }

    protected List<IGestureDetector> getGestureDetectors() {
        if (detectors == null) {
            detectors = initGestureDetectors(new ArrayList<IGestureDetector>());
        }
        return detectors;
    }

    protected List<IGestureDetector> initGestureDetectors(final List<IGestureDetector> list) {
        final AdvGuestureDetector listener = new AdvGuestureDetector(this, base.getListener());
        list.add(listener.innerDetector);
        list.add(new MultiTouchGestureDetector(listener));
        list.add(new DefaultGestureDetector(base.getContext(), listener));
        return list;
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#getView()
     */
    @Override
    public final IView getView() {
        return base.getView();
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#getBase()
     */
    @Override
    public final IActivityController getBase() {
        return base;
    }

    @Override
    public final void init(final IProgressIndicator task) {
        if (!isInitialized) {
            try {
                model.initPages(base, task);
            } finally {
                isInitialized = true;
            }
        }
    }

    @Override
    public boolean isInitialized() {
        return isInitialized;
    }

    /**
     *
     */
    @Override
    public final void onDestroy() {
        // isShown = false;
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#show()
     */
    @Override
    public final void show() {
        if (!isInitialized) {
            return;
        }
        if (!isShown) {
            isShown = true;

            invalidatePageSizes(InvalidateSizeReason.INIT, null);

            final AppBook bs = SettingsManager.getBookSettings();

            PageIndex currentPage = bs.getCurrentPage(getBase().getDocumentModel().getPageCount());
            int toPage = currentPage != null ? currentPage.docIndex : 0;

            if (AppState.get().isAlwaysOpenOnPage1) {
                toPage = 0;
            }


            goToPage(toPage, bs.x, bs.y);

        }
    }

    protected final void updatePosition(final Page page, final ViewState viewState) {
        if (page != null) {
            final PointF pos = viewState.getPositionOnPage(page);
            SettingsManager.positionChanged(pos.x, pos.y);
        }
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.core.events.ZoomListener#zoomChanged(float, float,
     * boolean)
     */
    @Override
    public final void zoomChanged(final float oldZoom, final float newZoom, final boolean committed) {
        if (!isShown) {
            return;
        }

        inZoom.set(!committed);
        EventPool.newEventZoom(this, oldZoom, newZoom, committed).process();

        if (!committed) {
            inQuickZoom.set(false);
        }
    }

    public final void quickZoom(final ActionEx action) {
        if (inZoom.get()) {
            return;
        }
        float zoomFactor = 2.0f;
        if (inQuickZoom.compareAndSet(true, false)) {
            zoomFactor = 1.0f / zoomFactor;
        } else {
            inQuickZoom.set(true);
        }
        base.getZoomModel().scaleAndCommitZoom(zoomFactor);
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#updateMemorySettings()
     */
    @Override
    public final void updateMemorySettings() {
        EventPool.newEventReset(this, null, false).process();
    }

    public final int getScrollX() {
        return getView().getScrollX();
    }

    public final int getWidth() {
        return getView().getWidth();
    }
    public final int getScrollY() {
        return getView().getScrollY();
    }

    public final int getHeight() {
        return getView().getHeight();
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#onTouchEvent(android.view.MotionEvent)
     */
    @Override
    public final boolean onTouchEvent(final MotionEvent ev) {
        if (AppState.get().isEditMode){
            return false;
        }
        for (final IGestureDetector d : getGestureDetectors()) {
            if (d.enabled() && d.onTouchEvent(ev)) {
                return true;
            }
        }

        return false;
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController
     * boolean, android.graphics.Rect, android.graphics.Rect)
     */
    @Override
    public boolean onLayoutChanged(final boolean layoutChanged) {
        if (layoutChanged) {
            if (isShown) {
                EventPool.newEventReset(this, InvalidateSizeReason.LAYOUT, true).process();
                return true;
            }
        }
        return false;
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#toggleRenderingEffects()
     */
    @Override
    public final void toggleRenderingEffects() {
        EventPool.newEventReset(this, null, true).process();
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#invalidateScroll()
     */
    @Override
    public final void invalidateScroll() {
        if (!isShown) {
            return;
        }
        getView().invalidateScroll();
    }

    /**
     * Sets the page align flag.
     *
     * @param align the new flag indicating align
     */
    @Override
    public final void setAlign(final PageAlign align) {
        EventPool.newEventReset(this, InvalidateSizeReason.PAGE_ALIGN, false).process();
    }

    /**
     * Checks if view is initialized.
     *
     * @return true, if is initialized
     */
    protected final boolean isShown() {
        return isShown;
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#getFirstVisiblePage()
     */
    @Override
    public int getFirstVisiblePage() {
        return firstVisiblePage;
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#getLastVisiblePage()
     */
    @Override
    public int getLastVisiblePage() {
        return lastVisiblePage;
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#redrawView()
     */
    @Override
    public final void redrawView() {
        getView().redrawView(new ViewState(this));
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.ui.viewer.IViewController#redrawView(org.ebookdroid.core.ViewState)
     */
    @Override
    public final void redrawView(final ViewState viewState) {
        getView().redrawView(viewState);
    }

    @Override
    public void clearSelectedText() {
        for (final Page page : model.getPages()) {
            page.selectedText.clear();
        }
        redrawView();
    }

    public final String processLongTap(boolean single, final MotionEvent e1, final MotionEvent e2, boolean draw) {
        if (e1 != null) {
            xLong = e1.getX();
            yLong = e1.getY();
        }

        float x2 = e2.getX();
        float y2 = e2.getY();

        final float zoom = base.getZoomModel().getZoom();

        // 创建一个矩形表示选择区域
        final RectF tapRect = new RectF(xLong, yLong, x2, y2);
        if (yLong > y2) {
            tapRect.sort();
        }
        tapRect.offset(getScrollX(), getScrollY());

        StringBuilder build = new StringBuilder();

        // 存储起始和结束的位置信息
        int startPageIndex = -1, endPageIndex = -1;
        int startLineIndex = -1, endLineIndex = -1;
        int startWordIndex = -1, endWordIndex = -1;
        float startX = Float.MAX_VALUE, endX = Float.MIN_VALUE;

        // 遍历可见页面，找到起始和结束位置
        for (final Page page : model.getPages(firstVisiblePage, lastVisiblePage + 1)) {
            final RectF bounds = page.getBounds(zoom);
            if (RectF.intersects(bounds, tapRect)) {
                if (LengthUtils.isNotEmpty(page.texts)) {
                    for (int i = 0; i < page.texts.length; i++) {
                        final TextWord[] lines = page.texts[i];
                        for (int j = 0; j < lines.length; j++) {
                            final TextWord word = lines[j];
                            if (!BookCSS.get().isTextFormat() && (word.left < 0 || word.top < 0)) {
                                continue;
                            }
                            RectF wordRect = page.getPageRegion(bounds, word);
                            if (wordRect == null) {
                                continue;
                            }
                            
                            // 找到起始位置
                            if (startPageIndex == -1 && wordRect.contains(tapRect.left, tapRect.top)) {
                                startPageIndex = page.index.docIndex;
                                startLineIndex = i;
                                startWordIndex = j;
                                startX = tapRect.left;
                            }
                            
                            // 找到结束位置
                            if (wordRect.contains(tapRect.right, tapRect.bottom)) {
                                endPageIndex = page.index.docIndex;
                                endLineIndex = i;
                                endWordIndex = j;
                                endX = tapRect.right;
                            }
                        }
                    }
                }
            }
        }
        
        // 如果没有找到起始位置，使用默认的简单选择逻辑
        if (startPageIndex == -1) {
            return simpleSelectText(tapRect, zoom, draw, build);
        }
        
        // 如果起始和结束在同一页，使用精确选择
        if (startPageIndex == endPageIndex) {
            return preciseSelectTextOnSamePage(startPageIndex, startLineIndex, startWordIndex, endLineIndex, endWordIndex, 
                                              startX, endX, zoom, draw, build);
        } else {
            // 否则使用跨页选择
            return selectTextAcrossPages(startPageIndex, startLineIndex, startWordIndex, endPageIndex, 
                                        endLineIndex, endWordIndex, startX, endX, zoom, draw, build);
        }
    }
    
    /**
     * 简单的文本选择逻辑，当无法精确确定起始位置时使用
     */
    private String simpleSelectText(RectF tapRect, float zoom, boolean draw, StringBuilder build) {
        boolean isHyphenWorld = false;
        TextWord prevWord = null;
        
        for (final Page page : model.getPages(firstVisiblePage, lastVisiblePage + 1)) {
            if (draw) {
                page.resetSelectionText();
            }
            final RectF bounds = page.getBounds(zoom);
            if (RectF.intersects(bounds, tapRect)) {
                if (LengthUtils.isNotEmpty(page.texts)) {
                    for (final TextWord[] lines : page.texts) {
                        for (final TextWord line : lines) {
                            if (!BookCSS.get().isTextFormat() && (line.left < 0 || line.top < 0)) {
                                continue;
                            }
                            RectF wordRect = page.getPageRegion(bounds, line);
                            if (wordRect == null) {
                                continue;
                            }
                            
                            // 只选择与tapRect相交的单词
                            if (RectF.intersects(wordRect, tapRect)) {
                                if (prevWord != null && prevWord.w.endsWith("-") && !isHyphenWorld) {
                                    if (draw) {
                                        page.selectedText.add(prevWord);
                                    }
                                    build.append(prevWord.w.replace("-", ""));
                                }

                                build.append(line.w + " ");
                                if (!isHyphenWorld) {
                                    if (draw) {
                                        page.selectedText.add(line);
                                    }
                                }

                                if (isHyphenWorld && TxtUtils.isNotEmpty(line.w)) {
                                    if (draw) {
                                        page.selectedText.add(line);
                                    }
                                    isHyphenWorld = false;
                                }

                                if (line.w.endsWith("-")) {
                                    isHyphenWorld = true;
                                }
                            }
                            if (TxtUtils.isNotEmpty(line.w)) {
                                prevWord = line;
                            }
                        }
                    }
                }
            }
        }
        
        return finishSelection(build, draw);
    }
    
    /**
     * 在同一页上精确选择文本
     */
    private String preciseSelectTextOnSamePage(int pageIndex, int startLineIndex, int startWordIndex, 
                                             int endLineIndex, int endWordIndex, float startX, float endX, 
                                             float zoom, boolean draw, StringBuilder build) {
        Page page = model.getPageByDocIndex(pageIndex);
        if (page == null || !LengthUtils.isNotEmpty(page.texts)) {
            return null;
        }
        
        if (draw) {
            page.resetSelectionText();
        }
        
        final RectF bounds = page.getBounds(zoom);
        boolean isHyphenWorld = false;
        TextWord prevWord = null;
        
        // 如果起始行和结束行相同
        if (startLineIndex == endLineIndex) {
            TextWord[] line = page.texts[startLineIndex];
            
            // 处理同一行的选择
            for (int j = startWordIndex; j <= endWordIndex; j++) {
                TextWord word = line[j];
                RectF wordRect = page.getPageRegion(bounds, word);
                if (wordRect == null) {
                    continue;
                }
                
                // 添加单词到选择
                build.append(word.w + " ");
                if (draw) {
                    page.selectedText.add(word);
                }
            }
        } else {
            // 处理多行选择
            // 1. 处理起始行
            TextWord[] startLine = page.texts[startLineIndex];
            for (int j = startWordIndex; j < startLine.length; j++) {
                TextWord word = startLine[j];
                build.append(word.w + " ");
                if (draw) {
                    page.selectedText.add(word);
                }
            }
            
            // 2. 处理中间行
            for (int i = startLineIndex + 1; i < endLineIndex; i++) {
                TextWord[] midLine = page.texts[i];
                for (TextWord word : midLine) {
                    build.append(word.w + " ");
                    if (draw) {
                        page.selectedText.add(word);
                    }
                }
            }
            
            // 3. 处理结束行
            TextWord[] endLine = page.texts[endLineIndex];
            for (int j = 0; j <= endWordIndex; j++) {
                TextWord word = endLine[j];
                build.append(word.w + " ");
                if (draw) {
                    page.selectedText.add(word);
                }
            }
        }
        
        return finishSelection(build, draw);
    }
    
    /**
     * 跨页选择文本
     */
    private String selectTextAcrossPages(int startPageIndex, int startLineIndex, int startWordIndex, 
                                       int endPageIndex, int endLineIndex, int endWordIndex, 
                                       float startX, float endX, float zoom, boolean draw, 
                                       StringBuilder build) {
        // 1. 处理起始页
        Page startPage = model.getPageByDocIndex(startPageIndex);
        if (startPage != null && LengthUtils.isNotEmpty(startPage.texts)) {
            if (draw) {
                startPage.resetSelectionText();
            }
            
            final RectF startBounds = startPage.getBounds(zoom);
            
            // 处理起始行剩余部分
            if (startLineIndex < startPage.texts.length) {
                TextWord[] startLine = startPage.texts[startLineIndex];
                for (int j = startWordIndex; j < startLine.length; j++) {
                    TextWord word = startLine[j];
                    build.append(word.w + " ");
                    if (draw) {
                        startPage.selectedText.add(word);
                    }
                }
            }
            
            // 处理起始页剩余行
            for (int i = startLineIndex + 1; i < startPage.texts.length; i++) {
                TextWord[] line = startPage.texts[i];
                for (TextWord word : line) {
                    build.append(word.w + " ");
                    if (draw) {
                        startPage.selectedText.add(word);
                    }
                }
            }
        }
        
        // 2. 处理中间页
        for (int p = startPageIndex + 1; p < endPageIndex; p++) {
            Page midPage = model.getPageByDocIndex(p);
            if (midPage != null && LengthUtils.isNotEmpty(midPage.texts)) {
                if (draw) {
                    midPage.resetSelectionText();
                }
                
                for (TextWord[] line : midPage.texts) {
                    for (TextWord word : line) {
                        build.append(word.w + " ");
                        if (draw) {
                            midPage.selectedText.add(word);
                        }
                    }
                }
            }
        }
        
        // 3. 处理结束页
        Page endPage = model.getPageByDocIndex(endPageIndex);
        if (endPage != null && LengthUtils.isNotEmpty(endPage.texts)) {
            if (draw) {
                endPage.resetSelectionText();
            }
            
            // 处理结束页的前几行
            for (int i = 0; i < endLineIndex; i++) {
                if (i < endPage.texts.length) {
                    TextWord[] line = endPage.texts[i];
                    for (TextWord word : line) {
                        build.append(word.w + " ");
                        if (draw) {
                            endPage.selectedText.add(word);
                        }
                    }
                }
            }
            
            // 处理结束行的前几个单词
            if (endLineIndex < endPage.texts.length) {
                TextWord[] endLine = endPage.texts[endLineIndex];
                for (int j = 0; j <= Math.min(endWordIndex, endLine.length - 1); j++) {
                    TextWord word = endLine[j];
                    build.append(word.w + " ");
                    if (draw) {
                        endPage.selectedText.add(word);
                    }
                }
            }
        }
        
        return finishSelection(build, draw);
    }
    
    /**
     * 完成文本选择并返回结果
     */
    private String finishSelection(StringBuilder build, boolean draw) {
        if (build.length() > 0) {
            redrawView();
            String txt = build.toString();
            try {
                if (txt.endsWith("- ")) {
                    TextWord[][] texts = model.getPageByDocIndex(firstVisiblePage + 1).texts;
                    if (texts[0].length > 1) {
                        txt += texts[0][1].w;
                    } else {
                        txt += texts[0][0].w;
                    }
                }
            } catch (Exception e) {
                LOG.e(e);
            }

            String filterString = TxtUtils.filterString(txt);
            LOG.d("Add Word SELECT-TEXT", filterString);
            LOG.d("Add Word SELECT-TEXT-ACTION");

            return filterString;
        }

        return null;
    }

    public final boolean processTap(final TouchManager.Touch type, final MotionEvent e) {
        final float x = e.getX();
        final float y = e.getY();

        if (type == Touch.SingleTap) {
            if (processLinkTap(x, y)) {
                return true;
            }
        }

        return processActionTap(type, x, y);
    }

    protected boolean processActionTap(final TouchManager.Touch type, final float x, final float y) {
        // final Integer actionId = TouchManager.getAction(type, x, y,
        // getWidth(), getHeight());
        final Integer actionId = null;
        final ActionEx action = actionId != null ? getOrCreateAction(actionId) : null;
        if (action != null && action.getMethod().isValid()) {
            action.run();
            return true;
        }
        return false;
    }

    public void selectAnnotation(Annotation annotation) {
        if (annotation == null) {
            for (final Page page : model.getPages(firstVisiblePage, lastVisiblePage + 1)) {
                page.selectionAnnotion = null;
            }
            base.getDocumentController().redrawView();
            return;
        }
        Page pageByDocIndex = base.getDocumentModel().getPageByDocIndex(annotation.getPage() - 1);
        pageByDocIndex.selectionAnnotion = annotation;
        base.getDocumentController().redrawView();
    }

    public final Annotation isAnnotationTap(final float x, final float y) {

        final float zoom = base.getZoomModel().getZoom();
        final RectF rect = new RectF(x, y, x, y);
        rect.offset(getScrollX(), getScrollY());

        for (final Page page : model.getPages(firstVisiblePage, lastVisiblePage + 1)) {
            final RectF bounds = page.getBounds(zoom);
            if (RectF.intersects(bounds, rect)) {
                if (page.annotations == null || page.annotations.isEmpty()) {
                    continue;
                }

                for (int i = 0; i < page.marks.size(); i++) {
                    Annotation a=page.annotations.get(Math.min(i,page.annotations.size()-1));
                    AppAnnotation aa=page.marks.get(i);
                    RectF wordRect = page.getPageRegion(bounds, aa.getAnnotationRect());
                    if (wordRect == null) {
                        continue;
                    }
                    boolean intersects = RectF.intersects(wordRect, rect);
                    if (intersects) {
                        LOG.d("测试注解更新",aa.page, aa.text,aa.textBefore);
                        DragingDialogs.dialogAnnotationTool(
                                getView().getView(),getBase().getListener(),
                                a,
                                aa,
                                wordRect.top - getScrollY());
                        return a;
                    }
                }

            }
        }
        return null;

    }

    protected final boolean processLinkTap(final float x, final float y) {
        final float zoom = base.getZoomModel().getZoom();
        final RectF rect = new RectF(x, y, x, y);
        rect.offset(getScrollX(), getScrollY());
        for (final Page page : model.getPages(firstVisiblePage, lastVisiblePage + 1)) {
            final RectF bounds = page.getBounds(zoom);
            if (RectF.intersects(bounds, rect)) {
                if (LengthUtils.isNotEmpty(page.links)) {
                    for (final PageLink link : page.links) {
                        if (processLinkTap(page, link, bounds, rect)) {
                            return true;
                        }
                    }
                }
                for (AppAnnotation mark : page.marks) {
                    if (processAnnotationTap(page, mark, bounds, rect)) {
                        return true;
                    }
                }
                return false;
            }
        }
        return false;
    }

    private boolean processAnnotationTap(Page page, AppAnnotation mark, RectF bounds, RectF tapRect) {
        boolean isTap=false;
        RectF maxRect=new RectF();
        boolean isFirst = true; // 标记是否是第一个矩形

        for (List<TextWord> line : mark.annotation) {
            RectF f = new RectF(line.get(0).left, line.get(0).top,
                    line.get(line.size() - 1).right, line.get(line.size() - 1).bottom);
            final RectF annotationRect = page.getPageRegion(bounds, f);
            if (annotationRect == null) {
                return false;
            }
            if (RectF.intersects(annotationRect, tapRect)){
                isTap=true;
            }
            // 如果是第一个矩形，直接设置为maxRect
            if (isFirst) {
                maxRect.set(annotationRect);
                isFirst = false;
            } else {
                // 计算当前maxRect和annotationRect的并集
                maxRect.union(annotationRect);
            }
        }
        if (isTap) {
            LOG.d("测试点击 annotation", maxRect);
            LOG.d("测试点击 tap", tapRect);
            LOG.d("测试删除 ", "路径1" + mark.path);
            PointF viewBase = getView().getBase(getView().getViewRect());
            maxRect.offset(-viewBase.x, -viewBase.y);
            DragingDialogs.dialogAnnotationTool(
                    getView().getView().getRootView(),
                    getBase().getListener(),
                    mark,
                    maxRect);
        }
        return isTap;
    }

    public int getLinkPage(final float x, final float y) {
        final float zoom = base.getZoomModel().getZoom();
        final RectF rect = new RectF(x, y, x, y);
        rect.offset(getScrollX(), getScrollY());

        for (final Page page : model.getPages(firstVisiblePage, lastVisiblePage + 1)) {
            final RectF bounds = page.getBounds(zoom);
            if (RectF.intersects(bounds, rect)) {
                if (LengthUtils.isNotEmpty(page.links)) {
                    for (final PageLink link : page.links) {
                        final RectF linkRect = page.getLinkSourceRect(bounds, link);

                        if (linkRect == null || !RectF.intersects(linkRect, rect)) {
                            return -1;
                        }

                        // if (link != null && link.url != null &&
                        // link.url.startsWith("http")) {
                        // openUrl(link.url);
                        // return true;
                        // }

                        if (link != null) {
                            return link.targetPage;
                        }

                    }
                }
                return -1;
            }
        }
        return -1;
    }

    protected final boolean processLinkTap(final Page page, final PageLink link, final RectF pageBounds, final RectF tapRect) {

        LOG.d("TEST", "processLinkTap");

        final RectF linkRect = page.getLinkSourceRect(pageBounds, link);

        if (linkRect == null || !RectF.intersects(linkRect, tapRect)) {
            return false;
        }

        if (link != null && link.url != null && link.url.startsWith("http")) {
            AlertDialogs.openUrl(base.getActivity(), link.url);
            return true;
        }

        if (link != null) {
            goToLink(link.targetPage, link.targetRect, true);
        }
        return true;
    }

    @Override
    public ViewState goToLink(final int pageDocIndex, final RectF targetRect, final boolean addToHistory) {
        if (pageDocIndex >= 0) {
            Page target = model.getPageByDocIndex(pageDocIndex);
            if (target == null) {
                return null;
            }
            float offsetX = 0;
            float offsetY = 0;
            if (targetRect != null) {
                offsetX = targetRect.left;
                offsetY = targetRect.top;
                if (target.type == PageType.LEFT_PAGE && offsetX >= 0.5f) {
                    target = model.getPageObject(target.index.viewIndex + 1);
                    offsetX -= 0.5f;
                }
            }
            if (target != null) {
                return base.jumpToPage(target.index.viewIndex, offsetX, offsetY, addToHistory);
            }
        }
        return null;
    }

//    protected class GestureListener extends SimpleOnGestureListener implements IMultiTouchListener {
//
//        /**
//         * {@inheritDoc}
//         *
//         * @see android.view.GestureDetector.SimpleOnGestureListener#onDoubleTap(android.view.MotionEvent)
//         */
//        @Override
//        public boolean onDoubleTap(final MotionEvent e) {
//            return processTap(TouchManager.Touch.DoubleTap, e);
//        }
//
//        /**
//         * {@inheritDoc}
//         *
//         * @see android.view.GestureDetector.SimpleOnGestureListener#onDown(android.view.MotionEvent)
//         */
//        @Override
//        public boolean onDown(final MotionEvent e) {
//            getView().forceFinishScroll();
//            return true;
//        }
//
//        /**
//         * {@inheritDoc}
//         *
//         * @see android.view.GestureDetector.SimpleOnGestureListener#onFling(android.view.MotionEvent,
//         * android.view.MotionEvent, float, float)
//         */
//        @Override
//        public boolean onFling(final MotionEvent e1, final MotionEvent e2, final float vX, final float vY) {
//            final Rect l = getScrollLimits();
//            float x = vX, y = vY;
//            if (Math.abs(vX / vY) < 0.5) {
//                x = 0;
//            }
//            if (Math.abs(vY / vX) < 0.5) {
//                y = 0;
//            }
//            getView().startFling(x, y, l);
//            getView().redrawView();
//            return true;
//        }
//
//        /**
//         * {@inheritDoc}
//         *
//         * @see android.view.GestureDetector.SimpleOnGestureListener#onScroll(android.view.MotionEvent,
//         * android.view.MotionEvent, float, float)
//         */
//        @Override
//        public boolean onScroll(final MotionEvent e1, final MotionEvent e2, final float distanceX, final float distanceY) {
//            float x = distanceX, y = distanceY;
//            if (Math.abs(distanceX / distanceY) < 0.5) {
//                x = 0;
//            }
//            if (Math.abs(distanceY / distanceX) < 0.5) {
//                y = 0;
//            }
//            getView().scrollBy((int) x, (int) y);
//            return true;
//        }
//
//        /**
//         * {@inheritDoc}
//         *
//         * @see android.view.GestureDetector.SimpleOnGestureListener#onSingleTapUp(android.view.MotionEvent)
//         */
//        @Override
//        public boolean onSingleTapUp(final MotionEvent e) {
//            return true;
//        }
//
//        /**
//         * {@inheritDoc}
//         *
//         * @see android.view.GestureDetector.SimpleOnGestureListener#onSingleTapConfirmed(android.view.MotionEvent)
//         */
//        @Override
//        public boolean onSingleTapConfirmed(final MotionEvent e) {
//            return processTap(TouchManager.Touch.SingleTap, e);
//        }
//
//        /**
//         * {@inheritDoc}
//         *
//         * @see android.view.GestureDetector.SimpleOnGestureListener#onLongPress(android.view.MotionEvent)
//         */
//        @Override
//        public void onLongPress(final MotionEvent e) {
//            // LongTap operation cause side-effects
//            // processTap(TouchManager.Touch.LongTap, e);
//        }
//
//
//        @Override
//        public void onTwoFingerPinch(final MotionEvent e, final float oldDistance, final float newDistance) {
//            final float factor = (float) Math.sqrt(newDistance / oldDistance);
//            base.getZoomModel().scaleZoom(factor);
//        }
//
//
//        @Override
//        public void onTwoFingerPinchEnd(final MotionEvent e) {
//            base.getZoomModel().commit();
//        }
//
//
//        @Override
//        public void onTwoFingerTap(final MotionEvent e) {
//            processTap(TouchManager.Touch.TwoFingerTap, e);
//        }
//    }

}
