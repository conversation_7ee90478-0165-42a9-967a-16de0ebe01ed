package com.tct.exbook.sys;

import android.content.Context;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.RectF;
import android.util.Log;
import android.view.GestureDetector.SimpleOnGestureListener;
import android.view.MotionEvent;
import android.widget.Toast;

import com.tct.exbook.AdamApp;
import com.tct.exbook.R;
import com.tct.exbook.android.utils.Dips;
import com.tct.exbook.android.utils.LOG;
import com.tct.exbook.android.utils.TxtUtils;
import com.tct.exbook.android.utils.Vibro;
import com.tct.exbook.model.AppSP;
import com.tct.exbook.model.AppState;
import com.tct.exbook.pdf.info.ExtUtils;
import com.tct.exbook.pdf.info.model.AnnotationType;
import com.tct.exbook.pdf.info.view.BrightnessHelper;
import com.tct.exbook.pdf.info.wrapper.DocumentController;
import com.tct.exbook.pdf.search.activity.msg.MessagePageXY;

import org.ebookdroid.BookType;
import org.ebookdroid.common.settings.SettingsManager;
import org.ebookdroid.common.touch.IGestureDetector;
import org.ebookdroid.common.touch.IMultiTouchListener;
import org.ebookdroid.common.touch.TouchManager;
import org.ebookdroid.core.AbstractViewController;
import org.ebookdroid.core.HScrollController;
import org.ebookdroid.core.Page;
import org.ebookdroid.core.PageCurlController;
import org.ebookdroid.core.VScrollController;
import org.ebookdroid.core.codec.Annotation;
import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

public class AdvGuestureDetector extends SimpleOnGestureListener implements IMultiTouchListener {

    private final AbstractViewController avc;
    private final DocumentController docCtrl;
    private boolean isTextFormat;

    private boolean isLongMovement = false;

    ClickUtils clickUtils;
    BrightnessHelper brightnessHelper;

    // 仿真翻页相关字段
    private boolean isCurlMode = false;
    private boolean isCurling = false;
    private float curlStartX = 0.0f;
    private float curlStartY = 0.0f;
    private float curlCurrentX = 0.0f;
    private float curlCurrentY = 0.0f;
    private PageCurlController.CurlDirection curlDirection;
    private static final float CURL_EDGE_THRESHOLD = 0.1f; // 10% 边缘区域
    private static final float CURL_PROGRESS_THRESHOLD = 0.3f; // 30% 翻页阈值

    public AdvGuestureDetector(final AbstractViewController avc, final DocumentController listener) {
        this.avc = avc;

        this.docCtrl = listener;
        try {
            String path = avc.base.getListener().getCurrentBook().getPath();
            isTextFormat = ExtUtils.isTextFomat(path) && !BookType.TXT.is(path);
        } catch (Exception e) {
            LOG.e(e);
        }

        contex = avc.getView().getView().getContext();

        clickUtils = new ClickUtils();
        updateBorders();
        brightnessHelper = new BrightnessHelper(contex);
        EventBus.getDefault().register(this);
    }

    public void updateBorders() {
        clickUtils.initMusician();
    }

    long time2 = 0;

    public IGestureDetector innerDetector = new IGestureDetector() {

        float x, y;
        float xInit, yInit;

        @Override
        public boolean onTouchEvent(MotionEvent ev) {
            // 检查是否是仿真翻页模式
            isCurlMode = (avc instanceof PageCurlController);

            // 如果是仿真翻页模式，优先处理翻页手势
            if (isCurlMode && handleCurlTouchEvent(ev)) {
                return true;
            }

            int delta = Dips.DP_5;
            int delta2 = Dips.DP_60;
            if (ev.getAction() == MotionEvent.ACTION_DOWN) {
                x = ev.getX();
                y = ev.getY();
                xInit = ev.getX();
                yInit = ev.getY();
            }
            if (ev.getAction() == MotionEvent.ACTION_UP) {
                final float zoom = avc.base.getZoomModel().getZoom();
                if (isLongMovement) {
                    if (TxtUtils.isNotEmpty(AppState.get().selectedText)) {
                        for (Page page : avc.model.getPages(avc.getFirstVisiblePage(), avc.getLastVisiblePage() + 1)) {
                            if (page.selectedText.isEmpty()) continue;
                            // todo 获取选中文本的矩形位置
                            RectF selectedTextRect = page.getSelectedTextRect(zoom);
                            Log.d("测试文本选中", "选中的矩形: " + selectedTextRect);
                            selectedTextRect = page.getPageRegion(page.getBounds(zoom), selectedTextRect);
                            PointF viewBase = avc.getView().getBase(avc.getView().getViewRect());
                            Log.d("测试文本选中 父view视图", avc.getView().getViewRect().toString());
                            selectedTextRect.offset(-viewBase.x, -viewBase.y);
                            Log.d("测试文本选中", "选中的矩形调整: " + selectedTextRect);
                            docCtrl.onLongPress(ev, selectedTextRect);
                        }
                    }
                } else if (Math.abs(x - ev.getX()) > delta2 && AdvGuestureDetector.this.avc instanceof VScrollController) {
                    if (x <= ev.getX()) {
                        docCtrl.onPrevPage(false);
                    } else {
                        docCtrl.onNextPage(false);
                    }
                } else if (Math.abs(y - ev.getY()) > delta2 && AdvGuestureDetector.this.avc instanceof HScrollController) {
                    if (y <= ev.getY()) {
                        docCtrl.onPrevPage(false);
                    } else {
                        docCtrl.onNextPage(false);
                    }
                }
                isLongMovement = false;
            }

            if (isLongMovement && (Math.abs(y - ev.getY()) > delta || Math.abs(x - ev.getX()) > delta)) {
                long d = System.currentTimeMillis() - time2;
                if (d > 150) {
                    time2 = System.currentTimeMillis();
                    AppState.get().selectedText = avc.processLongTap(false, null, ev, true);

                    time2 = System.currentTimeMillis();
                }
                x = ev.getX();
                y = ev.getY();

                if (TxtUtils.isNotEmpty(AppState.get().selectedText)) {
                    EventBus.getDefault().post(new MessagePageXY(MessagePageXY.TYPE_SHOW, -1, xInit, yInit, x, y));
                }

            }
            return false;
        }

        @Override
        public boolean enabled() {
            return true;
        }
    };

    /**
     * {@inheritDoc}
     *
     * @see android.view.GestureDetector.SimpleOnGestureListener#onDoubleTap(android.view.MotionEvent)
     */
    @Override
    public boolean onDoubleTap(final MotionEvent e) {
        if (clickUtils.isClickCenter(e.getX(), e.getY())) {
            docCtrl.onDoubleTap((int) e.getX(), (int) e.getY());
            // listener.onZoomInc();
        }
        return true;
    }

    private boolean isScrollFinished = true;
    private Context contex;

    @Override
    public boolean onDown(final MotionEvent e) {
        EventBus.getDefault().post(new MessagePageXY(MessagePageXY.TYPE_HIDE));
        isScrollFinished = avc.getView().getScroller().isFinished();
        if (!isScrollFinished) {
            avc.getView().getScroller().forceFinished(true);
            isScrollFinished = true;
        }

        clickUtils.initMusician();

        brightnessHelper.onActoinDown(e.getX(), e.getY());

        return true;
    }

    Annotation annotation;

    @Override
    public boolean onSingleTapConfirmed(final MotionEvent e) {
        // if (isScrollFinished) {

        if (alowConfirm && clickUtils.isClickCenter(e.getX(), e.getY())) {
            docCtrl.onSingleTap();
        }
        // }
        return true;
    }

    boolean alowConfirm = false;

    @Override
    public boolean onSingleTapUp(final MotionEvent e) {
        updateBorders();
        if (AppState.get().editWith != AppState.EDIT_DELETE && docCtrl.closeDialogs()) {
            alowConfirm = false;
            return true;
        }

        // 如果当前有选中文本，且点击位置在选区之外，则清除选中状态
        try {
            boolean hasSelection = false;
            boolean tapInsideSelection = false;
            final float zoom = avc.base.getZoomModel().getZoom();
            for (Page page : avc.model.getPages(avc.getFirstVisiblePage(), avc.getLastVisiblePage() + 1)) {
                if (page.selectedText == null || page.selectedText.isEmpty()) continue;
                hasSelection = true;
                RectF selectedTextRect = page.getSelectedTextRect(zoom);
                if (selectedTextRect == null) continue;
                // 转为视图坐标
                selectedTextRect = page.getPageRegion(page.getBounds(zoom), selectedTextRect);
                PointF viewBase = avc.getView().getBase(avc.getView().getViewRect());
                selectedTextRect.offset(-viewBase.x, -viewBase.y);
                if (selectedTextRect.contains(e.getX(), e.getY())) {
                    tapInsideSelection = true;
                    break;
                }
            }
            if (hasSelection && !tapInsideSelection) {
                docCtrl.clearSelectedText();
                AppState.get().selectedText = null;
                EventBus.getDefault().post(new MessagePageXY(MessagePageXY.TYPE_HIDE));
                alowConfirm = false;
                return true;
            }
        } catch (Throwable ignore) {}

        if (!(AppSP.get().readingMode == AppState.READING_MODE_MUSICIAN) && !AppState.get().isIgnoreAnnotatations || AppState.get().editWith == AppState.EDIT_DELETE) {
            alowConfirm = false;
            Annotation annotation2 = avc.isAnnotationTap(e.getX(), e.getY());

            if (annotation2 == null && annotation != null) {
                annotation = null;
                avc.selectAnnotation(null);
                return true;
            }
            annotation = annotation2;

            if (annotation != null) {
                avc.selectAnnotation(annotation);
                //todo  pdf注释

                if (AppState.get().editWith == AppState.EDIT_DELETE) {
                    docCtrl.onAnnotationTap(annotation.getPageHandler(), annotation.getPage(), annotation.getIndex());
                    avc.selectAnnotation(null);
                    annotation = null;
                } else {
                    if (annotation.type == AnnotationType.TEXT) {
                        docCtrl.showAnnotation(annotation);
                    } else {
                        docCtrl.showEditDialogIfNeed();
                    }
                }
                return true;
            }

            if (isTextFormat) {
                if (!TempHolder.isSeaching) {
                    String text = avc.processLongTap(true, e, e, false);
                    if (TxtUtils.isFooterNote(text)) {
                        AppState.get().selectedText = text;
                        final float zoom = avc.base.getZoomModel().getZoom();
                        avc.processLongTap(true, e, e, true);
                        for (Page page : avc.model.getPages(avc.getFirstVisiblePage(), avc.getLastVisiblePage() + 1)) {
                            if (page.selectedText.isEmpty()) continue;
                            RectF selectedTextRect = page.getSelectedTextRect(zoom);
                            Log.d("测试文本选中", "选中的矩形: " + selectedTextRect);
                            selectedTextRect = page.getPageRegion(page.getBounds(zoom), selectedTextRect);
                            PointF viewBase = avc.getView().getBase(avc.getView().getViewRect());
                            Log.d("测试文本选中 父view视图", avc.getView().getViewRect().toString());
                            selectedTextRect.offset(-viewBase.x, -viewBase.y);
                            Log.d("测试文本选中", "选中的矩形调整: " + selectedTextRect);
                            docCtrl.onLongPress(e, selectedTextRect);
                        }
                        return false;
                    }
                    if (TxtUtils.isNotEmpty(text)) {
                        docCtrl.clearSelectedText();
                        // docCtrl.closeFooterNotesDialog();
                        AppState.get().selectedText = null;
                        EventBus.getDefault().post(new MessagePageXY(MessagePageXY.TYPE_HIDE));

                    }
                }
            }

        }
        if (!(AppSP.get().readingMode == AppState.READING_MODE_MUSICIAN)) {
            boolean processTap = avc.processTap(TouchManager.Touch.SingleTap, e);
            if (processTap) {
                return false;
            }
        }

        if (clickUtils.isClickRight(e.getX(), e.getY())) {
            docCtrl.onRightPress();

        } else if (clickUtils.isClickLeft(e.getX(), e.getY())) {
            docCtrl.onLeftPress();

        } else if (clickUtils.isClickTop(e.getX(), e.getY())) {
            docCtrl.onTopPress();

        } else if (clickUtils.isClickBottom(e.getX(), e.getY())) {
            docCtrl.onBottomPress();

        } else if (clickUtils.isClickCenter(e.getX(), e.getY())) {
            // docCtrl.onSingleTap();
            alowConfirm = true;
            return false;
        }

        return true;
    }

    /**
     * {@inheritDoc}
     *
     * @see android.view.GestureDetector.SimpleOnGestureListener#onFling(android.view.MotionEvent,
     * android.view.MotionEvent, float, float)
     */
    @Override
    public boolean onFling(final MotionEvent e1, final MotionEvent e2, final float vX, final float vY) {
        try {

            if (AppState.get().isBrighrnessEnable && e1.getX() < BrightnessHelper.BRIGHTNESS_WIDTH) {
                return false;
            }

            if (AppSP.get().readingMode == AppState.READING_MODE_MUSICIAN) {
                return false;
            }
            final Rect l = avc.getScrollLimits();
            float x = vX, y = vY;
            if (Math.abs(vX / vY) < 0.5) {
                x = 0;
            }
            if (Math.abs(vY / vX) < 0.5) {
                y = 0;
            }

            if (this.avc instanceof VScrollController) {
                avc.getView().startFling(0, y, l);
            } else if (this.avc instanceof HScrollController){
                HScrollController hScrollController= (HScrollController) this.avc;
                // 如果向左移动，则向左滚动，否则向右滚动
                int dx=0;
                if (e1.getX() < e2.getX()){
                    dx = -(int) hScrollController.getPageWidth();
                }else{
                    dx = (int) hScrollController.getPageWidth();
                }
                avc.getView().startPageScroll(dx, 0);
            }

            avc.getView().redrawView();

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * {@inheritDoc}
     *
     * @see android.view.GestureDetector.SimpleOnGestureListener#onScroll(android.view.MotionEvent,
     * android.view.MotionEvent, float, float)
     */
    long t;

    int d1, d2;

    @Override
    public boolean onScroll(final MotionEvent e1, final MotionEvent e2, final float distanceX, final float distanceY) {
        if (docCtrl.checkReadingTimer()) {
            return true;
        }

        if (avc instanceof HScrollController)return true;
        final float x = distanceX, y = distanceY;
        d1 += x;
        d2 += y;

        if (brightnessHelper.onActionMove(e2)) {
            return true;
        }

        long delta = System.currentTimeMillis() - t;
        long value = AppSP.get().isLocked ? 5 : 10;

        if (delta > value) {
            t = System.currentTimeMillis();
            if (isNoLock() || (e2.getPointerCount() == 2 && !(AppSP.get().readingMode == AppState.READING_MODE_MUSICIAN) && AppState.get().isZoomInOutWithLock)) {
                avc.getView().scrollBy(d1, d2);
            } else {
                if (this.avc instanceof VScrollController) {
                    avc.getView().scrollBy(0, d2);
                } else {
                    avc.getView().scrollBy(d1, 0);
                }
            }
            d1 = d2 = 0;
            LOG.d("onScroll yes", avc.getView().getScrollY(), avc.getView().getHeight(), avc.getScrollLimits().bottom);
        }
        return true;
    }

    private boolean isNoLock() {
        return !AppSP.get().isLocked;
    }

    /**
     * {@inheritDoc}
     *
     * @see android.view.GestureDetector.SimpleOnGestureListener#onLongPress(android.view.MotionEvent)
     */
    @Override
    public void onLongPress(final MotionEvent e) {
        LOG.d("ADV-onLongPress");
        if (!AppState.get().isAllowTextSelection) {
            Toast.makeText(AdamApp.context, R.string.text_highlight_mode_is_disable, Toast.LENGTH_LONG).show();
            return;
        }
        Vibro.vibrate();
        if (AppSP.get().isCut || AppSP.get().isCrop) {
            Toast.makeText(AdamApp.context, R.string.the_page_is_clipped_the_text_selection_does_not_work, Toast.LENGTH_LONG).show();
            return;
        }

        isLongMovement = true;

        if (SettingsManager.getBookSettings() != null && SettingsManager.getBookSettings().cp) {
            docCtrl.onCrop();
        }
        AppState.get().selectedText = avc.processLongTap(true, e, e, true);
    }

    @Subscribe
    public void onSelectTextByAnchors(MessagePageXY event) {
        Log.d("测试", "显示文本选择工具3");
        if (MessagePageXY.TYPE_SELECT_TEXT == event.getType()) {

            float x = event.getX() - Dips.DP_36;
            float y = event.getY() - Dips.DP_36 / 2;

            MotionEvent e1 = MotionEvent.obtain(0, 0, 0, x, y, 0);
            MotionEvent e2 = MotionEvent.obtain(0, 0, 0, event.getX1(), event.getY1(), 0);
            AppState.get().selectedText = avc.processLongTap(false, e1, e2, true);
        }

    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.common.touch.IMultiTouchListener#onTwoFingerPinch(float,
     * float)
     */

    long time = 0;
    float factor1 = 1;

    @Override
    public void onTwoFingerPinch(final MotionEvent e, final float oldDistance, final float newDistance) {
        LOG.d("onTwoFingerPinch", oldDistance, newDistance);
        if (AppSP.get().isLocked) {
            if (AppSP.get().readingMode == AppState.READING_MODE_MUSICIAN) {
                return;
            }
            if (!AppState.get().isZoomInOutWithLock) {
                return;
            }
        }

        final float factor = (float) Math.sqrt(newDistance / oldDistance);
        factor1 *= factor;
        long delta = System.currentTimeMillis() - time;
        if (delta > 25) {
            time = System.currentTimeMillis();
            avc.base.getZoomModel().scaleZoom(factor1);
            factor1 = 1;
        }
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.common.touch.IMultiTouchListener#onTwoFingerPinchEnd()
     */
    @Override
    public void onTwoFingerPinchEnd(final MotionEvent e) {
        if (AppSP.get().isLocked) {
            if (AppSP.get().readingMode == AppState.READING_MODE_MUSICIAN) {
                return;
            }
            if (!AppState.get().isZoomInOutWithLock) {
                return;
            }
        }

        avc.base.getZoomModel().commit();
    }

    /**
     * {@inheritDoc}
     *
     * @see org.ebookdroid.common.touch.IMultiTouchListener#onTwoFingerTap()
     */
    @Override
    public void onTwoFingerTap(final MotionEvent e) {
        if (isScrollFinished && clickUtils.isClickCenter(e.getX(), e.getY()) && avc.getView().getScroller().isFinished()) {
            docCtrl.onSingleTap();
        }
    }

    /**
     * 处理仿真翻页的触摸事件
     */
    private boolean handleCurlTouchEvent(MotionEvent ev) {
        if (!(avc instanceof PageCurlController)) {
            return false;
        }

        PageCurlController curlController = (PageCurlController) avc;
        final float x = ev.getX();
        final float y = ev.getY();
        final int width = avc.getWidth();
        final int height = avc.getHeight();

        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                return handleCurlTouchDown(x, y, width, height, curlController);

            case MotionEvent.ACTION_MOVE:
                return handleCurlTouchMove(x, y, width, height, curlController);

            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                return handleCurlTouchUp(x, y, width, height, curlController);
        }
        return false;
    }

    /**
     * 处理仿真翻页的触摸按下事件
     */
    private boolean handleCurlTouchDown(float x, float y, int width, int height, PageCurlController curlController) {
        // 检查是否在页面边缘区域（用于翻页）
        final float edgeThreshold = width * CURL_EDGE_THRESHOLD;

        if (x < edgeThreshold) {
            // 左边缘，准备向右翻页（上一页）
            if (avc.model.getCurrentDocPageIndex() > 0) {
                startCurl(x, y, PageCurlController.CurlDirection.RIGHT_TO_LEFT, curlController);
                return true;
            }
        } else if (x > width - edgeThreshold) {
            // 右边缘，准备向左翻页（下一页）
            if (avc.model.getCurrentDocPageIndex() < avc.model.getPageCount() - 1) {
                startCurl(x, y, PageCurlController.CurlDirection.LEFT_TO_RIGHT, curlController);
                return true;
            }
        }
        return false;
    }

    /**
     * 处理仿真翻页的触摸移动事件
     */
    private boolean handleCurlTouchMove(float x, float y, int width, int height, PageCurlController curlController) {
        if (isCurling) {
            updateCurl(x, y, width, height, curlController);
            return true;
        }
        return false;
    }

    /**
     * 处理仿真翻页的触摸抬起事件
     */
    private boolean handleCurlTouchUp(float x, float y, int width, int height, PageCurlController curlController) {
        if (isCurling) {
            finishCurl(width, height, curlController);
            return true;
        }
        return false;
    }

    /**
     * 开始翻页
     */
    private void startCurl(float x, float y, PageCurlController.CurlDirection direction, PageCurlController curlController) {
        LOG.d("测试仿真翻页", "开始翻页|方向=" + direction);
        isCurling = true;
        curlDirection = direction;
        curlStartX = x;
        curlStartY = y;
        curlCurrentX = x;
        curlCurrentY = y;

        // 通知PageCurlController开始翻页
        curlController.startCurl(x, y, direction);

        // 触发重绘
        avc.getView().redrawView();
    }

    /**
     * 更新翻页状态
     */
    private void updateCurl(float x, float y, int width, int height, PageCurlController curlController) {
        curlCurrentX = x;
        curlCurrentY = y;

        // 计算翻页进度
        float deltaX = Math.abs(x - curlStartX);
        float progress = Math.min(deltaX / width, 1.0f);

        LOG.d("测试仿真翻页", "更新翻页|进度=" + progress);

        // 通知PageCurlController更新翻页状态
        curlController.updateCurl(x, y, progress);

        // 触发重绘
        avc.getView().redrawView();
    }

    /**
     * 完成翻页
     */
    private void finishCurl(int width, int height, PageCurlController curlController) {
        // 计算最终进度
        float deltaX = Math.abs(curlCurrentX - curlStartX);
        float progress = Math.min(deltaX / width, 1.0f);

        LOG.d("测试仿真翻页", "完成翻页|进度=" + progress + "|阈值=" + CURL_PROGRESS_THRESHOLD);

        if (progress > CURL_PROGRESS_THRESHOLD) {
            // 超过阈值，完成翻页
            int currentPage = avc.model.getCurrentDocPageIndex();
            int targetPage = curlDirection == PageCurlController.CurlDirection.LEFT_TO_RIGHT ?
                currentPage + 1 : currentPage - 1;

            LOG.d("测试仿真翻页", "翻页完成|目标页面=" + targetPage);

            // 执行翻页
            if (curlDirection == PageCurlController.CurlDirection.LEFT_TO_RIGHT) {
                docCtrl.onNextPage(false);
            } else {
                docCtrl.onPrevPage(false);
            }
        } else {
            LOG.d("测试仿真翻页", "翻页取消|进度不足");
        }

        // 重置翻页状态
        resetCurlState(curlController);
    }

    /**
     * 重置翻页状态
     */
    private void resetCurlState(PageCurlController curlController) {
        isCurling = false;
        curlStartX = 0.0f;
        curlStartY = 0.0f;
        curlCurrentX = 0.0f;
        curlCurrentY = 0.0f;

        // 通知PageCurlController重置状态
        curlController.resetCurlState();

        // 触发重绘
        avc.getView().redrawView();
    }

}
